import request from '@/utils/request'

// 创建外贸订单项次档
export function createContractTradeDetail(data) {
  return request({
    url: '/sms/contract-trade-detail/create',
    method: 'post',
    data: data
  })
}

// 更新外贸订单项次档
export function updateContractTradeDetail(data) {
  return request({
    url: '/sms/contract-trade-detail/update',
    method: 'put',
    data: data
  })
}

// 删除外贸订单项次档
export function deleteContractTradeDetail(id) {
  return request({
    url: '/sms/contract-trade-detail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得外贸订单项次档
export function getContractTradeDetail(id) {
  return request({
    url: '/sms/contract-trade-detail/get?id=' + id,
    method: 'get'
  })
}

// 获得外贸订单项次档分页
export function getContractTradeDetailPage(params) {
  return request({
    url: '/sms/contract-trade-detail/page',
    method: 'get',
    params
  })
}
// 导出外贸订单项次档 Excel
export function exportContractTradeDetailExcel(params) {
  return request({
    url: '/sms/contract-trade-detail/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
