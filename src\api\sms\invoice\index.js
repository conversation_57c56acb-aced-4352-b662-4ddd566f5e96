import request from '@/utils/request'

// 创建提货单主档
export function createInvoice(data) {
  return request({
    url: '/sms/invoice/create',
    method: 'post',
    data: data
  })
}

// 更新提货单主档
export function updateInvoice(data) {
  return request({
    url: '/sms/invoice/update',
    method: 'put',
    data: data
  })
}
export function invoiceTakeEffect(id) {
  return request({
    url: '/sms/invoice/invoiceTakeEffect?id='+id,
    method: 'get',
  })
}
export function invoiceTakeIneffective(id) {
  return request({
    url: '/sms/invoice/invoiceTakeIneffective?id='+id,
    method: 'get',
  })
}
export function invoiceEndcase(id) {
  return request({
    url: '/sms/invoice/invoiceEndcase?id='+id,
    method: 'get',
  })
}

// 删除提货单主档
export function deleteInvoice(id) {
  return request({
    url: '/sms/invoice/delete?id=' + id,
    method: 'delete'
  })
}

// 获得提货单主档
export function getInvoice(id) {
  return request({
    url: '/sms/invoice/get?id=' + id,
    method: 'get'
  })
}

// 获得提货单主档分页
export function getInvoicePage(params) {
  return request({
    url: '/sms/invoice/page',
    method: 'get',
    params
  })
}
// 导出提货单主档 Excel
export function exportInvoiceExcel(params) {
  return request({
    url: '/sms/invoice/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（提货单项次档） ====================
    // 获得提货单项次档分页
  export function getInvoiceDetailPage(params) {
    return request({
      url: '/sms/invoice/invoice-detail/page',
      method: 'get',
      params
    })
  }
  export function getInvoiceDetailPageByParams(params) {
    return request({
      url: '/sms/invoice/invoice-detail/pageDetailByParams',
      method: 'get',
      params
    })
  }
        // 新增提货单项次档
  export function createInvoiceDetail(data) {
    return request({
      url: '/sms/invoice/invoice-detail/create',
      method: 'post',
      data
    })
  }
  // 修改提货单项次档
  export function updateInvoiceDetail(data) {
    return request({
      url: '/sms/invoice/invoice-detail/update',
      method: 'post',
      data
    })
  }
  // 删除提货单项次档
  export function deleteInvoiceDetail(id) {
    return request({
      url: '/sms/invoice/invoice-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得提货单项次档
  export function getInvoiceDetail(id) {
    return request({
      url: '/sms/invoice/invoice-detail/get?id=' + id,
      method: 'get'
    })
  }
  export function ifSameDetail(data) {
    return request({
      url: '/sms/invoice/invoice-detail/ifSameDetail',
      method: 'post',
      data
    })
  }

export function getInvoiceDetailSearchPageByParams(params) {
  return request({
    url: '/sms/invoice/invoice-detail/pageDetailSearchByParams',
    method: 'get',
    params
  })
}

export function updateInvoiceDetailForCommerce(data) {
  return request({
    url: '/sms/invoice/invoice-detail/updateForCommerce',
    method: 'post',
    data
  })
}

export function queryTakeEffect(id) {
  return request({
    url: '/sms/invoice/invoice-detail/queryTakeEffect?id=' + id,
    method: 'get'
  })
}
export function queryTakeIneffective(id) {
  return request({
    url: '/sms/invoice/invoice-detail/queryTakeIneffective?id=' + id,
    method: 'get'
  })
}
export function invoiceBack(id) {
  return request({
    url: '/sms/invoice/invoice-detail/invoiceBack?id='+id,
    method: 'get',
  })
}
