import request from '@/utils/request'

// 创建信用证业务登记
export function createLCRegister(data) {
  return request({
    url: '/pms/LC-register/create',
    method: 'post',
    data: data
  })
}

// 更新信用证业务登记
export function updateLCRegister(data) {
  return request({
    url: '/pms/LC-register/update',
    method: 'put',
    data: data
  })
}

// 删除信用证业务登记
export function deleteLCRegister(id) {
  return request({
    url: '/pms/LC-register/delete?id=' + id,
    method: 'delete'
  })
}

// 获得信用证业务登记
export function getLCRegister(id) {
  return request({
    url: '/pms/LC-register/get?id=' + id,
    method: 'get'
  })
}

// 获得信用证业务登记分页
export function getLCRegisterPage(params) {
  return request({
    url: '/pms/LC-register/page',
    method: 'get',
    params
  })
}
// 导出信用证业务登记 Excel
export function exportLCRegisterExcel(params) {
  return request({
    url: '/pms/LC-register/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 删除信用证业务登记
export function confirmLCRegister(id,flag) {
  return request({
    url: '/pms/LC-register/confirm?id=' + id + '&flag=' + flag,
    method: 'get'
  })
}
