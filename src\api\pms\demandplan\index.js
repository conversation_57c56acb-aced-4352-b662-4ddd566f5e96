import request from '@/utils/request'

// 创建需求计划申报
export function createDemandPlan(data) {
  return request({
    url: '/pms/demand-plan/create',
    method: 'post',
    data: data
  })
}

// 更新需求计划申报
export function updateDemandPlan(data) {
  return request({
    url: '/pms/demand-plan/update',
    method: 'put',
    data: data
  })
}

// 删除需求计划申报
export function deleteDemandPlan(id) {
  return request({
    url: '/pms/demand-plan/delete?id=' + id,
    method: 'delete'
  })
}

// 获得需求计划申报
export function getDemandPlan(id) {
  return request({
    url: '/pms/demand-plan/get?id=' + id,
    method: 'get'
  })
}

// 获得需求计划申报分页
export function getDemandPlanPage(params) {
  return request({
    url: '/pms/demand-plan/page',
    method: 'get',
    params
  })
}

// 获得需求计划申报分页-不按照当前登录用户查询
export function getDemandPlanPage2(params) {
  return request({
    url: '/pms/demand-plan/page2',
    method: 'get',
    params
  })
}

// 导出需求计划申报 Excel
export function exportDemandPlanExcel(params) {
  return request({
    url: '/pms/demand-plan/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 提交需求计划申报
export function submitDemandPlan(data) {
  return request({
    url: '/pms/demand-plan/submit?',
    method: 'post',
    data
  })
}

// 退回需求计划申报
export function backDemandPlan(id) {
  return request({
    url: '/pms/demand-plan/back?id=' + id,
    method: 'get'
  })
}

// 获取批量上传模板文件下载路径
export function getTemplateFileUrl() {
  return request({
    url: '/pms/demand-plan/getTemplateFileUrl',
    method: 'get'
  })
}
