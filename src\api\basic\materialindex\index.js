import request from '@/utils/request'

// 创建物料料号索引
export function createMaterialIndex(data) {
  return request({
    url: '/basic/material-index/create',
    method: 'post',
    data: data
  })
}

// 更新物料料号索引
export function updateMaterialIndex(data) {
  return request({
    url: '/basic/material-index/update',
    method: 'put',
    data: data
  })
}

// 删除物料料号索引
export function deleteMaterialIndex(id) {
  return request({
    url: '/basic/material-index/delete?id=' + id,
    method: 'delete'
  })
}

// 获得物料料号索引
export function getMaterialIndex(id) {
  return request({
    url: '/basic/material-index/get?id=' + id,
    method: 'get'
  })
}

// 获得物料料号索引分页
export function getMaterialIndexPage(params) {
  return request({
    url: '/basic/material-index/page',
    method: 'get',
    params
  })
}

// 获得全部物料料号索引
export function getMaterialIndexList(params) {
  return request({
    url: '/basic/material-index/list',
    method: 'get',
    params
  })
}
// 通过父类的料号索引获得数据
export function listByParentMatrlIndexid(parentMatrlIndexid) {
  return request({
    url: '/basic/material-index/listByParentMatrlIndexid?parentMatrlIndexid='+parentMatrlIndexid,
    method: 'get',
  })
}
// 导出物料料号索引 Excel
export function exportMaterialIndexExcel(params) {
  return request({
    url: '/basic/material-index/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 下载用户导入模板
export function importTemplate() {
  return request({
    url: '/basic/material-index/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}

export function getTreeListByLevel(level) {
  return request({
    url: '/basic/material-index/getTreeListByLevel?level=' + level,
    method: 'get'
  })
}
export function getMaterialIndexByMatrlIndexidAndType(type, code) {
  return request({
    url: '/basic/material-index/getByCodeAndType?code=' + code + '&type=' + type,
    method: 'get'
  })
}
