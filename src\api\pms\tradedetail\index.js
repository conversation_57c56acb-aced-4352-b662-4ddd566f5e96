import request from '@/utils/request'

// 创建交易申请明细
export function createTradeDetail(data) {
  return request({
    url: '/pms/trade-detail/create',
    method: 'post',
    data: data
  })
}

// 更新交易申请明细
export function updateTradeDetail(data) {
  return request({
    url: '/pms/trade-detail/update',
    method: 'put',
    data: data
  })
}

// 删除交易申请明细
export function deleteTradeDetail(id) {
  return request({
    url: '/pms/trade-detail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得交易申请明细
export function getTradeDetail(id) {
  return request({
    url: '/pms/trade-detail/get?id=' + id,
    method: 'get'
  })
}

// 获得交易申请明细分页
export function getTradeDetailPage(params) {
  return request({
    url: '/pms/trade-detail/page',
    method: 'get',
    params
  })
}
// 导出交易申请明细 Excel
export function exportTradeDetailExcel(params) {
  return request({
    url: '/pms/trade-detail/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
