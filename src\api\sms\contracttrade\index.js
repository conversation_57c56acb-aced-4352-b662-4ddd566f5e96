import request from '@/utils/request'

// 创建外贸订单主档
export function createContractTrade(data) {
  return request({
    url: '/sms/contract-trade/create',
    method: 'post',
    data: data
  })
}

// 更新外贸订单主档
export function updateContractTrade(data) {
  return request({
    url: '/sms/contract-trade/update',
    method: 'put',
    data: data
  })
}

// 删除外贸订单主档
export function deleteContractTrade(id) {
  return request({
    url: '/sms/contract-trade/delete?id=' + id,
    method: 'delete'
  })
}

// 作废外贸订单主档
export function wasteContractTrade(data) {
  return request({
    url: '/sms/contract-trade/waste',
    method: 'put',
    data: data
  })
}

// 获得外贸订单主档
export function getContractTrade(id) {
  return request({
    url: '/sms/contract-trade/get?id=' + id,
    method: 'get'
  })
}

// 获得外贸订单主档分页
export function getContractTradePage(params) {
  return request({
    url: '/sms/contract-trade/page',
    method: 'get',
    params
  })
}
// 导出外贸订单主档 Excel
export function exportContractTradeExcel(params) {
  return request({
    url: '/sms/contract-trade/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 生效流程
export function effectiveContract(id) {
  return request({
    url: '/sms/contract-trade/effect?id=' + id,
    method: 'get'
  })
}
