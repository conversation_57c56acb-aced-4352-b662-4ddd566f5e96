import request from '@/utils/request'

// 创建合同版本管理主表
export function createNewVersion(id) {
  return request({
    url: '/pms/po-main-v/create?id=' + id,
    method: 'get'
  })
}

// 更新合同版本管理主表
export function updatePoMainV(data) {
  return request({
    url: '/pms/po-main-v/update',
    method: 'put',
    data: data
  })
}

// 删除合同版本管理主表
export function deletePoMainV(id) {
  return request({
    url: '/pms/po-main-v/delete?id=' + id,
    method: 'delete'
  })
}

// 获得合同版本管理主表
export function getPoMainV(id) {
  return request({
    url: '/pms/po-main-v/get?id=' + id,
    method: 'get'
  })
}

// 获得合同版本管理主表
export function getAllPoMainV(parentId) {
  return request({
    url: '/pms/po-main-v/get-all?parentId=' + parentId,
    method: 'get'
  })
}

export function getPoMainVByPre(poNo,poVer) {
  return request({
    url: '/pms/po-main-v/getPre?poNo=' + poNo+"&poVer="+poVer,
    method: 'get'
  })
}

// 获得合同版本管理主表分页
export function getPoMainVPage(params) {
  return request({
    url: '/pms/po-main-v/page',
    method: 'get',
    params
  })
}
// 导出合同版本管理主表 Excel
export function exportPoMainVExcel(params) {
  return request({
    url: '/pms/po-main-v/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（合同版本管理明细） ====================
// 获得合同版本管理明细列表
export function getPoDetailVListByParentId(params) {
  return request({
    url: '/pms/po-main-v/po-detail-v/list-by-parent-id',
    method: 'get',
    params
  })
}

// ==================== 子表（合同版本管理涉及的条款内容） ====================
// 获得合同版本管理涉及的条款内容列表
export function getPoTocVListByParentId(params) {
  return request({
    url: '/pms/po-main-v/po-toc-v/list-by-parent-id',
    method: 'get',
    params
  })
}

// 获得合同明细
export function getPoDetailV(id) {
  return request({
    url: '/pms/po-main-v/po-detail-v/get?id=' + id,
    method: 'get'
  })
}

// 获得合同条款内容
export function getPoTocV(id) {
  return request({
    url: '/pms/po-main-v/po-toc-v/get?id=' + id,
    method: 'get'
  })
}

// 修改合同明细
export function updatePoDetailV(data) {
  return request({
    url: '/pms/po-main-v/po-detail-v/update',
    method: 'post',
    data
  })
}

// 修改合同条款内容
export function updatePoTocV(data) {
  return request({
    url: '/pms/po-main-v/po-toc-v/update',
    method: 'post',
    data
  })
}

// 新增合同明细
export function createPoDetailV(data) {
  return request({
    url: '/pms/po-main-v/po-detail-v/create',
    method: 'post',
    data
  })
}
// 新增合同条款内容
export function createPoTocV(data) {
  return request({
    url: '/pms/po-main-v/po-toc-v/create',
    method: 'post',
    data
  })
}

// 按采购案项次批量新增
export function batchInsertDetailVs(data) {
  return request({
    url: '/pms/po-main-v/po-detail-v/batch',
    method: 'post',
    data
  })
}

// 按物料项次批量新增
export function batchInsertDetailVs2(data) {
  return request({
    url: '/pms/po-main-v/po-detail-v/batch2',
    method: 'post',
    data
  })
}

// 按采购案项次批量新增
export function batchInsertDetailVsInq(data) {
  return request({
    url: '/pms/po-main-v/po-detail-v/batchInq',
    method: 'post',
    data
  })
}
