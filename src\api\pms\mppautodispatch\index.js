import request from '@/utils/request'

// 创建采购计划自动调度规则
export function createMppAutoDispatch(data) {
  return request({
    url: '/pms/mpp-auto-dispatch/create',
    method: 'post',
    data: data
  })
}

// 更新采购计划自动调度规则
export function updateMppAutoDispatch(data) {
  return request({
    url: '/pms/mpp-auto-dispatch/update',
    method: 'put',
    data: data
  })
}

// 删除采购计划自动调度规则
export function deleteMppAutoDispatch(id) {
  return request({
    url: '/pms/mpp-auto-dispatch/delete?id=' + id,
    method: 'delete'
  })
}

// 获得采购计划自动调度规则
export function getMppAutoDispatch(id) {
  return request({
    url: '/pms/mpp-auto-dispatch/get?id=' + id,
    method: 'get'
  })
}

// 获得采购计划自动调度规则分页
export function getMppAutoDispatchPage(params) {
  return request({
    url: '/pms/mpp-auto-dispatch/page',
    method: 'get',
    params
  })
}
// 导出采购计划自动调度规则 Excel
export function exportMppAutoDispatchExcel(params) {
  return request({
    url: '/pms/mpp-auto-dispatch/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
