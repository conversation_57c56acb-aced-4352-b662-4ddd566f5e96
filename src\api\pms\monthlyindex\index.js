import request from '@/utils/request'

// 创建日月指数
export function createMonthlyIndex(data) {
  return request({
    url: '/pms/monthly-index/create',
    method: 'post',
    data: data
  })
}

// 更新日月指数
export function updateMonthlyIndex(data) {
  return request({
    url: '/pms/monthly-index/update',
    method: 'put',
    data: data
  })
}

// 删除日月指数
export function deleteMonthlyIndex(id) {
  return request({
    url: '/pms/monthly-index/delete?id=' + id,
    method: 'delete'
  })
}

// 获得日月指数
export function getMonthlyIndex(id,cmt) {
  return request({
    url: '/pms/monthly-index/get?id=' + id+'&cmt='+cmt,
    method: 'get'
  })
}

// 获得日月指数分页
export function getMonthlyIndexPage(params) {
  return request({
    url: '/pms/monthly-index/page',
    method: 'get',
    params
  })
}

// 导出日月指数 Excel
export function exportMonthlyIndexExcel(params) {
  return request({
    url: '/pms/monthly-index/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
export function importTemplate(titles) {
  return request({
    url: '/pms/monthly-index/get-import-template?titles='+titles,
    method: 'get',
    responseType: 'blob'
  })
}
// 获得日月指数分页
export function genMonthIndex(params) {
  return request({
    url: '/pms/monthly-index/genMonthIndex',
    method: 'post',
    data: params
  })
}
