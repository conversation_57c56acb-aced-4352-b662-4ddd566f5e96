import request from '@/utils/request'

// 创建船运资源管理
export function createShipResource(data) {
  return request({
    url: '/sms/ship-resource/create',
    method: 'post',
    data: data
  })
}

// 更新船运资源管理
export function updateShipResource(data) {
  return request({
    url: '/sms/ship-resource/update',
    method: 'put',
    data: data
  })
}

// 删除船运资源管理
export function deleteShipResource(id) {
  return request({
    url: '/sms/ship-resource/delete?id=' + id,
    method: 'delete'
  })
}

// 获得船运资源管理
export function getShipResource(id) {
  return request({
    url: '/sms/ship-resource/get?id=' + id,
    method: 'get'
  })
}

// 获得船运资源管理分页
export function getShipResourcePage(params) {
  return request({
    url: '/sms/ship-resource/page',
    method: 'get',
    params
  })
}
// 导出船运资源管理 Excel
export function exportShipResourceExcel(params) {
  return request({
    url: '/sms/ship-resource/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（船运资源管理明细） ====================
    // 获得船运资源管理明细分页
  export function getShipResourceDetailPage(params) {
    return request({
      url: '/sms/ship-resource/ship-resource-detail/page',
      method: 'get',
      params
    })
  }

// 获得船运资源管理明细分页
export function getAllShipResourceDetailPage(params) {
  return request({
    url: '/sms/ship-resource/ship-resource-detail/allpage',
    method: 'get',
    params
  })
}
        // 新增船运资源管理明细
  export function createShipResourceDetail(data) {
    return request({
      url: '/sms/ship-resource/ship-resource-detail/create',
      method: 'post',
      data
    })
  }
  // 修改船运资源管理明细
  export function updateShipResourceDetail(data) {
    return request({
      url: '/sms/ship-resource/ship-resource-detail/update',
      method: 'post',
      data
    })
  }
  // 删除船运资源管理明细
  export function deleteShipResourceDetail(id) {
    return request({
      url: '/sms/ship-resource/ship-resource-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得船运资源管理明细
  export function getShipResourceDetail(id) {
    return request({
      url: '/sms/ship-resource/ship-resource-detail/get?id=' + id,
      method: 'get'
    })
  }
