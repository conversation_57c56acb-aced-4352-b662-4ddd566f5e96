import request from '@/utils/request'

// 创建物料属性变更审核
export function createMaterialAudit(data) {
  return request({
    url: '/basic/material-audit/create',
    method: 'post',
    data: data
  })
}

// 更新物料属性变更审核
export function updateMaterialAudit(data) {
  return request({
    url: '/basic/material-audit/update',
    method: 'put',
    data: data
  })
}

// 删除物料属性变更审核
export function deleteMaterialAudit(id) {
  return request({
    url: '/basic/material-audit/delete?id=' + id,
    method: 'delete'
  })
}

// 获得物料属性变更审核
export function getMaterialAudit(id) {
  return request({
    url: '/basic/material-audit/get?id=' + id,
    method: 'get'
  })
}

// 获得物料属性变更审核分页
export function getMaterialAuditPage(params) {
  return request({
    url: '/basic/material-audit/page',
    method: 'get',
    params
  })
}
// 导出物料属性变更审核 Excel
export function exportMaterialAuditExcel(params) {
  return request({
    url: '/basic/material-audit/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
export function getNoComplateByParentid(parentid,type) {
  return request({
    url: '/basic/material-audit/getNoComplateByParentid?parentid=' + parentid + '&type=' + type,
    method: 'get'
  })
}

