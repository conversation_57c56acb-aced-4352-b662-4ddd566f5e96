import request from '@/utils/request'

// 创建采购计划主档
export function createMpp(data) {
  return request({
    url: '/pms/mpp/create',
    method: 'post',
    data: data
  })
}

// 更新采购计划主档
export function updateMpp(data) {
  return request({
    url: '/pms/mpp/update',
    method: 'put',
    data: data
  })
}
// 更新采购计划主档
export function updateAuditMpp(data) {
  return request({
    url: '/pms/mpp//update-audit',
    method: 'put',
    data: data
  })
}

// 删除采购计划主档
export function deleteMpp(id) {
  return request({
    url: '/pms/mpp/delete?id=' + id,
    method: 'delete'
  })
}

// 获得采购计划主档
export function getMpp(id) {
  return request({
    url: '/pms/mpp/get?id=' + id,
    method: 'get'
  })
}

// 获得采购计划主档分页
export function getMppPage(params) {
  return request({
    url: '/pms/mpp/page',
    method: 'get',
    params
  })
}
// 导出采购计划主档 Excel
export function exportMppExcel(params) {
  return request({
    url: '/pms/mpp/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（采购计划明细） ====================
    // 获得采购计划明细分页
  export function getMppDetailPage(params) {
    return request({
      url: '/pms/mpp/mpp-detail/page',
      method: 'get',
      params
    })
  }

// ==================== 子表（采购计划明细） ====================
// 获得采购计划明细分页
export function getMppDetailPage2(params) {
  return request({
    url: '/pms/mpp/mpp-detail/page2',
    method: 'get',
    params
  })
}
        // 新增采购计划明细
  export function createMppDetail(data) {
    return request({
      url: '/pms/mpp/mpp-detail/create',
      method: 'post',
      data
    })
  }
  // 修改采购计划明细
  export function updateMppDetail(data) {
    return request({
      url: '/pms/mpp/mpp-detail/update',
      method: 'put',
      data
    })
  }
  // 删除采购计划明细
  export function deleteMppDetail(id) {
    return request({
      url: '/pms/mpp/mpp-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得采购计划明细
  export function getMppDetail(id) {
    return request({
      url: '/pms/mpp/mpp-detail/get?id=' + id,
      method: 'get'
    })
  }
  //获取模板
  export function importTemplate() {
    return request({
      url: '/pms/mpp/get-import-template',
      method: 'get',
      responseType: 'blob'
    })
  }
// ==================== 子表（采购计划明细） ====================
export function createMppProcess(data) {
  return request({
    url: '/pms/mpp/process/create',
    method: 'post',
    data
  })
}

export function cancelMpp(data) {
  return request({
    url: '/pms/mpp/process/cancel',
    method: 'post',
    data
  })
}
