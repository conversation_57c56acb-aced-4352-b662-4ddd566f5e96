/**
 * 将毫秒，转换成时间字符串。例如说，xx 分钟
 *
 * @param ms 毫秒
 * @returns {string} 字符串
 */
export function getDate(ms) {
  const day = Math.floor(ms / (24 * 60 * 60 * 1000));
  const hour = Math.floor((ms / (60 * 60 * 1000) - day * 24));
  const minute = Math.floor(((ms / (60 * 1000)) - day * 24 * 60 - hour * 60));
  const second = Math.floor((ms / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - minute * 60));
  if (day > 0) {
    return day + "天" + hour + "小时" + minute + "分钟";
  }
  if (hour > 0) {
    return hour + "小时" + minute + "分钟";
  }
  if (minute > 0) {
    return minute + "分钟";
  }
  if (second > 0) {
    return second + "秒";
  } else {
    return 0 + "秒";
  }
}

export function beginOfDay(date) {
  return new Date(date.getFullYear(), date.getMonth(), date.getDate());
}

export function endOfDay(date) {
  return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999);
}

export function betweenDay(date1, date2) {
  date1 = convertDate(date1);
  date2 = convertDate(date2);
  // 计算差值
  return Math.floor((date2.getTime() - date1.getTime()) / (24 * 3600 * 1000));
}

export function formatDate(date, fmt) {
  date = convertDate(date);
  const o = {
    "M+": date.getMonth() + 1, //月份
    "d+": date.getDate(), //日
    "H+": date.getHours(), //小时
    "m+": date.getMinutes(), //分
    "s+": date.getSeconds(), //秒
    "q+": Math.floor((date.getMonth() + 3) / 3), //季度
    "S": date.getMilliseconds() //毫秒
  };
  if (/(y+)/.test(fmt)) { // 年份
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
  }
  for (const k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    }
  }
  return fmt;
}

export function addTime(date, time) {
  date = convertDate(date);
  return new Date(date.getTime() + time);
}

export function convertDate(date) {
  if (typeof date === 'string') {
    return new Date(date);
  }
  return date;
}


export function getCurrentDate(n) {
  let dd = new Date();
  if (n) {
    dd.setDate(dd.getDate() - n);
  }
  let year = dd.getFullYear();
  let month = dd.getMonth() + 1;
  let day = dd.getDate();
  return year + "-" +
    (month < 10 ? "0" : "") + month + "-" +
    (day < 10 ? "0" : "") + day;
}

export function getCurrentYearMon(n) {
  let dd = new Date();
  if (n) {
    dd.setDate(dd.getDate() - n);
  }
  let year = dd.getFullYear();
  let month = dd.getMonth() + 1;
  let day = dd.getDate();
  return year + "-" + (month < 10 ? "0" : "") + month
}

export function getCurrentTime() {
  let dd = new Date();
  let hour = dd.getHours();
  let minutes = dd.getMinutes();
  let seconds = dd.getSeconds();
  return (hour < 10 ? "0" : "") + hour + ":" +
    (minutes < 10 ? "0" : "") + minutes + ":" +
    (seconds < 10 ? "0" : "") + seconds;
}

export function getCurrentDateTime(n) {
  let dd = new Date();
  if (n) {
    dd.setDate(dd.getDate() - n);
  }
  let year = dd.getFullYear();
  let month = dd.getMonth() + 1;
  let day = dd.getDate();
  let hour = dd.getHours();
  let minutes = dd.getMinutes();
  let seconds = dd.getSeconds();
  return year + "-" +
    (month < 10 ? "0" : "") + month + "-" +
    (day < 10 ? "0" : "") + day + " " +
    (hour < 10 ? "0" : "") + hour + ":" +
    (minutes < 10 ? "0" : "") + minutes + ":" +
    (seconds < 10 ? "0" : "") + seconds;
}

export function getNowDate(n) {
  let now = new Date();
  if (n) {
    now.setDate(now.getDate() - n);
  }
  let year = now.getFullYear(); //得到年份
  let month = (now.getMonth() + 1).toString().padStart(2, "0"); //得到月份
  let day = now.getDate().toString().padStart(2, "0"); //得到日期
  return `${year}${month}${day}`;
}

export function getCurrentDateTimeMill(n) {
  const date = new Date();
  if (n) {
    date.setDate(date.getDate() - n);
  }

  // 3. 定义一个用于数字前置补零的辅助函数
  const pad = (num, length = 2) => String(num).padStart(length, '0');

  // 4. 获取各个时间部件并正确补零
  const year = date.getFullYear();
  const month = pad(date.getMonth() + 1); // 月份从0开始，所以要+1
  const day = pad(date.getDate());
  const hour = pad(date.getHours());
  const minutes = pad(date.getMinutes());
  const seconds = pad(date.getSeconds());
  const milliseconds = pad(date.getMilliseconds(), 3); // 毫秒需要补零到3位

  // 5. 使用模板字符串拼接并返回结果
  return `${year}${month}${day}${hour}${minutes}${seconds}${milliseconds}`;
}
