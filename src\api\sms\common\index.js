import request from '@/utils/request'

// 获得新体系产品型态分页
export function getProdTypeBgPage(params) {
  return request({
    url: '/sms/common/prodtypebg/page',
    method: 'get',
    params
  })
}

// 获得老体系产品型态分页
export function getProdTypeBg2Page(params) {
  return request({
    url: '/sms/common/prodtypebg2/page',
    method: 'get',
    params
  })
}

// 根据公司别获得新老体系产品型态分页
export function getProdTypePage(params) {
  return request({
    url: '/sms/common/prodtype/page',
    method: 'get',
    params
  })
}

// 获得产品规范编号分页
export function getPsrNoBgPage(params) {
  return request({
    url: '/sms/common/psrnobg/page',
    method: 'get',
    params
  })
}

// 获得产品规范编号分页
export function getPsrNoBg2Page(params) {
  return request({
    url: '/sms/common/psrnobg2/page',
    method: 'get',
    params
  })
}

// 根据公司别获得新老体系产品规范分页
export function getPsrNoPage(params) {
  return request({
    url: '/sms/common/psrno/page',
    method: 'get',
    params
  })
}

