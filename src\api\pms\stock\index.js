import request from '@/utils/request'

// 创建料号实时库存
export function createWmsStock(data) {
  return request({
    url: '/pms/wms-stock/create',
    method: 'post',
    data: data
  })
}

// 更新料号实时库存
export function updateWmsStock(data) {
  return request({
    url: '/pms/wms-stock/update',
    method: 'put',
    data: data
  })
}

// 删除料号实时库存
export function deleteWmsStock(id) {
  return request({
    url: '/pms/wms-stock/delete?id=' + id,
    method: 'delete'
  })
}

// 获得料号实时库存
export function getWmsStock(id) {
  return request({
    url: '/pms/wms-stock/get?id=' + id,
    method: 'get'
  })
}

// 获得料号实时库存分页
export function getWmsStockPage(params) {
  return request({
    url: '/pms/wms-stock/page',
    method: 'get',
    params
  })
}
// 导出料号实时库存 Excel
export function exportWmsStockExcel(params) {
  return request({
    url: '/pms/wms-stock/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 通过料号获取库存汇总值
export function getEndQtySumByMatrlno(id) {
  return request({
    url: '/pms/wms-stock/getEndQtySumByMatrlno?matrlno=' + id,
    method: 'get'
  })
}
