import request from '@/utils/request'

// 创建待结算明细
export function createTradeDetail2(data) {
  return request({
    url: '/pms/wrs-trade-detail2/create',
    method: 'post',
    data: data
  })
}

// 更新待结算明细
export function updateTradeDetail2(data) {
  return request({
    url: '/pms/wrs-trade-detail2/update',
    method: 'put',
    data: data
  })
}

// 删除待结算明细
export function deleteTradeDetail2(id) {
  return request({
    url: '/pms/wrs-trade-detail2/delete?id=' + id,
    method: 'delete'
  })
}

// 获得待结算明细
export function getTradeDetail2(id) {
  return request({
    url: '/pms/wrs-trade-detail2/get?id=' + id,
    method: 'get'
  })
}

// 获得待结算明细分页
export function getTradeDetail2Page(params) {
  return request({
    url: '/pms/wrs-trade-detail2/page',
    method: 'get',
    params
  })
}

// 获得待结算明细分页
export function getTradeDetail2SettlePage(params) {
  return request({
    url: '/pms/wrs-trade-detail2/settlePage',
    method: 'get',
    params
  })
}

// 导出待结算明细 Excel
export function exportTradeDetail2Excel(params) {
  return request({
    url: '/pms/wrs-trade-detail2/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
