import request from '@/utils/request'

// 创建销售调整原因主档
export function createAdjustmentMaster(data) {
  return request({
    url: '/sms/adjustment-master/create',
    method: 'post',
    data: data
  })
}

// 更新销售调整原因主档
export function updateAdjustmentMaster(data) {
  return request({
    url: '/sms/adjustment-master/update',
    method: 'put',
    data: data
  })
}

// 删除销售调整原因主档
export function deleteAdjustmentMaster(id) {
  return request({
    url: '/sms/adjustment-master/delete?id=' + id,
    method: 'delete'
  })
}

// 获得销售调整原因主档
export function getAdjustmentMaster(id) {
  return request({
    url: '/sms/adjustment-master/get?id=' + id,
    method: 'get'
  })
}

// 获得销售调整原因主档分页
export function getAdjustmentMasterPage(params) {
  return request({
    url: '/sms/adjustment-master/page',
    method: 'get',
    params
  })
}
// 导出销售调整原因主档 Excel
export function exportAdjustmentMasterExcel(params) {
  return request({
    url: '/sms/adjustment-master/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（销售调整原因明细档） ====================
    // 获得销售调整原因明细档分页
  export function getAdjustmentDetailPage(params) {
    return request({
      url: '/sms/adjustment-master/adjustment-detail/page',
      method: 'get',
      params
    })
  }
        // 新增销售调整原因明细档
  export function createAdjustmentDetail(data) {
    return request({
      url: '/sms/adjustment-master/adjustment-detail/create',
      method: 'post',
      data
    })
  }
  // 修改销售调整原因明细档
  export function updateAdjustmentDetail(data) {
    return request({
      url: '/sms/adjustment-master/adjustment-detail/update',
      method: 'post',
      data
    })
  }
  // 删除销售调整原因明细档
  export function deleteAdjustmentDetail(id) {
    return request({
      url: '/sms/adjustment-master/adjustment-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得销售调整原因明细档
  export function getAdjustmentDetail(id) {
    return request({
      url: '/sms/adjustment-master/adjustment-detail/get?id=' + id,
      method: 'get'
    })
  }