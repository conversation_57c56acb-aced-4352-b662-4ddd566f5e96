import request from '@/utils/request'

// 创建料号详细信息
export function createMaterialDetail(data) {
  return request({
    url: '/basic/material-detail/create',
    method: 'post',
    data: data
  })
}

// 更新料号详细信息
export function updateMaterialDetail(data) {
  return request({
    url: '/basic/material-detail/update',
    method: 'put',
    data: data
  })
}

// 删除料号详细信息
export function deleteMaterialDetail(id) {
  return request({
    url: '/basic/material-detail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得料号详细信息
export function getMaterialDetail(id) {
  return request({
    url: '/basic/material-detail/get?id=' + id,
    method: 'get'
  })
}

// 获得料号详细信息分页
export function getMaterialDetailPage(params) {
  return request({
    url: '/basic/material-detail/page',
    method: 'get',
    params
  })
}
// 导出料号详细信息 Excel
export function exportMaterialDetailExcel(params) {
  return request({
    url: '/basic/material-detail/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
