import request from '@/utils/request'

// 创建销售调整资料档
export function createSaleAdjust(data) {
  return request({
    url: '/sms/sale-adjust/create',
    method: 'post',
    data: data
  })
}

// 更新销售调整资料档
export function updateSaleAdjust(data) {
  return request({
    url: '/sms/sale-adjust/update',
    method: 'put',
    data: data
  })
}

// 删除销售调整资料档
export function deleteSaleAdjust(id) {
  return request({
    url: '/sms/sale-adjust/delete?id=' + id,
    method: 'delete'
  })
}

// 获得销售调整资料档
export function getSaleAdjust(id) {
  return request({
    url: '/sms/sale-adjust/get?id=' + id,
    method: 'get'
  })
}

// 获得销售调整资料档分页
export function getSaleAdjustPage(params) {
  return request({
    url: '/sms/sale-adjust/page',
    method: 'get',
    params
  })
}
// 导出销售调整资料档 Excel
export function exportSaleAdjustExcel(params) {
  return request({
    url: '/sms/sale-adjust/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
export function checkHaveEndcase(data) {
  return request({
    url: '/sms/sale-adjust/checkHaveEndcase',
    method: 'post',
    data: data
  })
}
export function effectiveSaleAdjust(id) {
  return request({
    url: '/sms/sale-adjust/effectiveSaleAdjust?id=' + id,
    method: 'get'
  })
}
export function invalidationSaleAdjust(id) {
  return request({
    url: '/sms/sale-adjust/invalidationSaleAdjust?id=' + id,
    method: 'get'
  })
}

