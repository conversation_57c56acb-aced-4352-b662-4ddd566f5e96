import request from '@/utils/request'

// 创建物料料号基础信息管理
export function createMaterial(data) {
  return request({
    url: '/basic/material/create',
    method: 'post',
    data: data
  })
}

// 更新物料料号基础信息管理
export function updateMaterial(data) {
  return request({
    url: '/basic/material/update',
    method: 'put',
    data: data
  })
}

// 删除物料料号基础信息管理
export function deleteMaterial(id) {
  return request({
    url: '/basic/material/delete?id=' + id,
    method: 'delete'
  })
}

// 获得物料料号基础信息管理
export function getMaterial(id) {
  return request({
    url: '/basic/material/get?id=' + id,
    method: 'get'
  })
}
export function getCnmdescByMatrlno(matrlno) {
  return request({
    url: '/basic/material/getCnmdescByMatrlno?matrlno=' + matrlno,
    method: 'get'
  })
}

// 获得物料料号基础信息管理分页
export function getMaterialPage(params) {
  return request({
    url: '/basic/material/page',
    method: 'get',
    params
  })
}

// 获得物料料号基础信息管理分页
export function getMaterialPage2(params) {
  return request({
    url: '/basic/material/page2',
    method: 'get',
    params
  })
}

// 导出物料料号基础信息管理 Excel
export function exportMaterialExcel(params) {
  return request({
    url: '/basic/material/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（料号详细信息） ====================
    // 获得料号详细信息分页
  export function getMaterialDetailPage(params) {
    return request({
      url: '/basic/material/material-detail/page',
      method: 'get',
      params
    })
  }
        // 新增料号详细信息
  export function createMaterialDetail(data) {
    return request({
      url: '/basic/material/material-detail/create',
      method: 'post',
      data
    })
  }
  // 修改料号详细信息
  export function updateMaterialDetail(data) {
    return request({
      url: '/basic/material/material-detail/update',
      method: 'post',
      data
    })
  }
  // 删除料号详细信息
  export function deleteMaterialDetail(id) {
    return request({
      url: '/basic/material/material-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得料号详细信息
  export function getMaterialDetail(id) {
    return request({
      url: '/basic/material/material-detail/get?id=' + id,
      method: 'get'
    })
  }
export function getMaterialDetailByMatrlno(matrlno) {
  return request({
    url: '/basic/material/material-detail/getByMatrlno?matrlno=' + matrlno,
    method: 'get'
  })
}

export function applyMaterial(id) {
  return request({
    url: '/basic/material/apply?id=' + id,
    method: 'get'
  })
}
export function batchApplyMaterial(data) {
  return request({
    url: '/basic/material/applyBatch?id',
    method: 'post',
    data
  })
}
export function getMaterialByIds(ids) {
  return request({
    url: '/basic/material/getByIds?ids=' + ids,
    method: 'get'
  })
}
// ==================== 子表（物料料号成分资料子） ====================
// 获得物料料号成分资料子分页
export function getMaterialDetailCompositionPage(params) {
  return request({
    url: '/basic/material/material-detail-composition/page',
    method: 'get',
    params
  })
}
// 新增物料料号成分资料子
export function createMaterialDetailComposition(data) {
  return request({
    url: '/basic/material/material-detail-composition/create',
    method: 'post',
    data
  })
}
// 修改物料料号成分资料子
export function updateMaterialDetailComposition(data) {
  return request({
    url: '/basic/material/material-detail-composition/update',
    method: 'post',
    data
  })
}
// 删除物料料号成分资料子
export function deleteMaterialDetailComposition(id) {
  return request({
    url: '/basic/material/material-detail-composition/delete?id=' + id,
    method: 'delete'
  })
}
// 获得物料料号成分资料子
export function getMaterialDetailComposition(id) {
  return request({
    url: '/basic/material/material-detail-composition/get?id=' + id,
    method: 'get'
  })
}

export function batchOperateDetailComposition(data) {
  return request({
    url: '/basic/material/material-detail-composition/batchOperateDetailComposition',
    method: 'post',
    data
  })
}
