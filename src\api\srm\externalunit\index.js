import request from '@/utils/request'

// 创建外部单位
export function createExternalUnit(data) {
  return request({
    url: '/srm/external-unit/create',
    method: 'post',
    data: data
  })
}

// 更新外部单位
export function updateExternalUnit(data) {
  return request({
    url: '/srm/external-unit/update',
    method: 'put',
    data: data
  })
}

// 删除外部单位
export function deleteExternalUnit(id) {
  return request({
    url: '/srm/external-unit/delete?id=' + id,
    method: 'delete'
  })
}

// 获得外部单位
export function getExternalUnit(id) {
  return request({
    url: '/srm/external-unit/get?id=' + id,
    method: 'get'
  })
}
export function getSupplierNameBySupplierCode(supplierCode) {
  return request({
    url: '/srm/external-unit/getSupplierNameBySupplierCode?supplierCode=' + supplierCode,
    method: 'get'
  })
}

// 获得外部单位分页
export function getExternalUnitPage(params) {
  return request({
    url: '/srm/external-unit/page',
    method: 'get',
    params
  })
}
// 导出外部单位 Excel
export function exportExternalUnitExcel(params) {
  return request({
    url: '/srm/external-unit/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
// ==================== 子表（银行信息） ====================
// 获得银行信息分页
export function getBankInformationPage(params) {
  return request({
    url: '/srm/external-unit/bank-information/page',
    method: 'get',
    params
  })
}
// 新增银行信息
export function createBankInformation(data) {
  return request({
    url: '/srm/external-unit/bank-information/create',
    method: 'post',
    data
  })
}
// 修改银行信息
export function updateBankInformation(data) {
  return request({
    url: '/srm/external-unit/bank-information/update',
    method: 'put',
    data
  })
}
// 删除银行信息
export function deleteBankInformation(id) {
  return request({
    url: '/srm/external-unit/bank-information/delete?id=' + id,
    method: 'delete'
  })
}
// 获得银行信息
export function getBankInformation(id) {
  return request({
    url: '/srm/external-unit/bank-information/get?id=' + id,
    method: 'get'
  })
}

// ==================== 子表（联系人） ====================
// 获得联系人分页
export function getContactsPage(params) {
  return request({
    url: '/srm/external-unit/contacts/page',
    method: 'get',
    params
  })
}
// 新增联系人
export function createContacts(data) {
  return request({
    url: '/srm/external-unit/contacts/create',
    method: 'post',
    data
  })
}
// 修改联系人
export function updateContacts(data) {
  return request({
    url: '/srm/external-unit/contacts/update',
    method: 'put',
    data
  })
}
// 删除联系人
export function deleteContacts(id) {
  return request({
    url: '/srm/external-unit/contacts/delete?id=' + id,
    method: 'delete'
  })
}
// 获得联系人
export function getContacts(id) {
  return request({
    url: '/srm/external-unit/contacts/get?id=' + id,
    method: 'get'
  })
}
