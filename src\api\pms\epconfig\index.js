import request from '@/utils/request'

// 创建账务管理
export function createEpConfig(data) {
  return request({
    url: '/pms/ep-config/create',
    method: 'post',
    data: data
  })
}

// 更新账务管理
export function updateEpConfig(data) {
  return request({
    url: '/pms/ep-config/update',
    method: 'put',
    data: data
  })
}

// 删除账务管理
export function deleteEpConfig(id) {
  return request({
    url: '/pms/ep-config/delete?id=' + id,
    method: 'delete'
  })
}

// 获得账务管理
export function getEpConfig(id) {
  return request({
    url: '/pms/ep-config/get?id=' + id,
    method: 'get'
  })
}

// 获得账务管理分页
export function getEpConfigPage(params) {
  return request({
    url: '/pms/ep-config/page',
    method: 'get',
    params
  })
}
// 导出账务管理 Excel
export function exportEpConfigExcel(params) {
  return request({
    url: '/pms/ep-config/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
