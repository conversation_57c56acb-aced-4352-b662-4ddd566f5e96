import request from '@/utils/request'

// 创建交货单主档
export function createDoMain(data) {
  return request({
    url: '/pms/do-main/create',
    method: 'post',
    data: data
  })
}

// 更新交货单主档
export function updateDoMain(data) {
  return request({
    url: '/pms/do-main/update',
    method: 'put',
    data: data
  })
}

// 删除交货单主档
export function deleteDoMain(id) {
  return request({
    url: '/pms/do-main/delete?id=' + id,
    method: 'delete'
  })
}

// 获得交货单主档
export function getDoMain(id) {
  return request({
    url: '/pms/do-main/get?id=' + id,
    method: 'get'
  })
}

// 获得交货单主档分页
export function getDoMainPage(params) {
  return request({
    url: '/pms/do-main/page',
    method: 'get',
    params
  })
}
// 获得交货单主档分页
export function getPoDoMainPage(params) {
  return request({
    url: '/pms/do-main/getPoDoPage',
    method: 'get',
    params
  })
}

// 导出交货单主档 Excel
export function exportDoMainExcel(params) {
  return request({
    url: '/pms/do-main/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 确认合同管理主表信息
export function confirmDoMain(id) {
  return request({
    url: '/pms/do-main/confirm?id=' + id+'&flag=',
    method: 'get'
  })
}

// 确认合同管理主表信息
export function cancelConfirmDoMain(id) {
  return request({
    url: '/pms/do-main/cancelConfirm?id=' + id,
    method: 'get'
  })
}


// ==================== 子表（交货单明细） ====================
    // 获得交货单明细分页
  export function getDoDetailPage(params) {
    return request({
      url: '/pms/do-main/do-detail/page',
      method: 'get',
      params
    })
  }
        // 新增交货单明细
  export function createDoDetail(data) {
    return request({
      url: '/pms/do-main/do-detail/create',
      method: 'post',
      data
    })
  }
  // 修改交货单明细
  export function updateDoDetail(data) {
    return request({
      url: '/pms/do-main/do-detail/update',
      method: 'post',
      data
    })
  }
  // 删除交货单明细
  export function deleteDoDetail(id) {
    return request({
      url: '/pms/do-main/do-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得交货单明细
  export function getDoDetail(id) {
    return request({
      url: '/pms/do-main/do-detail/get?id=' + id,
      method: 'get'
    })
  }

// 批量新增
export function batchInsertDoDetails(data) {
  return request({
    url: '/pms/do-main/do-detail/batch',
    method: 'post',
    data: data
  })
}
