import request from '@/utils/request'

// 创建收款作业主档
export function createColl(data) {
  return request({
    url: '/sms/coll/create',
    method: 'post',
    data: data
  })
}

// 更新收款作业主档
export function updateColl(data) {
  return request({
    url: '/sms/coll/update',
    method: 'put',
    data: data
  })
}

// 删除收款作业主档
export function deleteColl(id) {
  return request({
    url: '/sms/coll/delete?id=' + id,
    method: 'delete'
  })
}

// 获得收款作业主档
export function getColl(id) {
  return request({
    url: '/sms/coll/get?id=' + id,
    method: 'get'
  })
}

// 获得收款作业主档分页
export function getCollPage(params) {
  return request({
    url: '/sms/coll/page',
    method: 'get',
    params
  })
}
// 导出收款作业主档 Excel
export function exportCollExcel(params) {
  return request({
    url: '/sms/coll/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function confirm(id) {
  return request({
    url: '/sms/coll/confirm?id=' + id,
    method: 'get'
  })
}
export function cancelConfirm(id) {
  return request({
    url: '/sms/coll/cancelConfirm?id=' + id,
    method: 'get'
  })
}
