import request from '@/utils/request'

// 创建询价记录
export function createEnquiry(data) {
  return request({
    url: '/cust/enquiry/create',
    method: 'post',
    data: data
  })
}

// 更新询价记录
export function updateEnquiry(data) {
  return request({
    url: '/cust/enquiry/update',
    method: 'put',
    data: data
  })
}

// 删除询价记录
export function deleteEnquiry(id) {
  return request({
    url: '/cust/enquiry/delete?id=' + id,
    method: 'delete'
  })
}

// 获得询价记录
export function getEnquiry(id) {
  return request({
    url: '/cust/enquiry/get?id=' + id,
    method: 'get'
  })
}

// 获得询价记录分页
export function getEnquiryPage(params) {
  return request({
    url: '/cust/enquiry/page',
    method: 'get',
    params
  })
}
// 导出询价记录 Excel
export function exportEnquiryExcel(params) {
  return request({
    url: '/cust/enquiry/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
