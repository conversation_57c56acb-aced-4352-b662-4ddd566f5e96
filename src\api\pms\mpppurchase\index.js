import request from '@/utils/request'

// 创建采购案
export function createMppPurchase(data) {
  return request({
    url: '/pms/mpp-purchase/create',
    method: 'post',
    data: data
  })
}

// 更新采购案
export function updateMppPurchase(data) {
  return request({
    url: '/pms/mpp-purchase/update',
    method: 'put',
    data: data
  })
}

// 删除采购案
export function deleteMppPurchase(id) {
  return request({
    url: '/pms/mpp-purchase/delete?id=' + id,
    method: 'delete'
  })
}

// 获得采购案
export function getMppPurchase(id) {
  return request({
    url: '/pms/mpp-purchase/get?id=' + id,
    method: 'get'
  })
}

// 获得采购案分页
export function getMppPurchasePage(params) {
  return request({
    url: '/pms/mpp-purchase/page',
    method: 'get',
    params
  })
}
// 导出采购案 Excel
export function exportMppPurchaseExcel(params) {
  return request({
    url: '/pms/mpp-purchase/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（采购案明细） ====================
    // 获得采购案明细分页
  export function getMppPurchasedDetailPage2(params) {
    return request({
      url: '/pms/mpp-purchase/mpp-purchased-detail/page2',
      method: 'get',
      params
    })
  }

// 获得采购案明细分页
export function getMppPurchasedDetailPage(params) {
  return request({
    url: '/pms/mpp-purchase/mpp-purchased-detail/page',
    method: 'get',
    params
  })
}
        // 新增采购案明细
  export function createMppPurchasedDetail(data) {
    return request({
      url: '/pms/mpp-purchase/mpp-purchased-detail/create',
      method: 'post',
      data
    })
  }
  // 修改采购案明细
  export function updateMppPurchasedDetail(data) {
    return request({
      url: '/pms/mpp-purchase/mpp-purchased-detail/update',
      method: 'post',
      data
    })
  }
  // 删除采购案明细
  export function deleteMppPurchasedDetail(id) {
    return request({
      url: '/pms/mpp-purchase/mpp-purchased-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得采购案明细
  export function getMppPurchasedDetail(id) {
    return request({
      url: '/pms/mpp-purchase/mpp-purchased-detail/get?id=' + id,
      method: 'get'
    })
  }
