import request from '@/utils/request'

// 创建客商评价
export function createEvaluate(data) {
  return request({
    url: '/cust/evaluate/create',
    method: 'post',
    data: data
  })
}

// 更新客商评价
export function updateEvaluate(data) {
  return request({
    url: '/cust/evaluate/update',
    method: 'put',
    data: data
  })
}

// 获得客商评价
export function getEvaluate(id) {
  return request({
    url: '/cust/evaluate/get?id=' + id,
    method: 'get'
  })
}

// 获得客商评价分页
export function getEvaluatePage(params) {
  return request({
    url: '/cust/evaluate/page',
    method: 'get',
    params
  })
}
// 导出客商评价 Excel
export function exportEvaluateExcel(params) {
  return request({
    url: '/cust/evaluate/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
