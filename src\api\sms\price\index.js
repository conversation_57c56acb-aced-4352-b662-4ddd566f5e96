import request from '@/utils/request'

// 创建价格建立和审批
export function createPrice(data) {
  return request({
    url: '/sms/price/create',
    method: 'post',
    data: data
  })
}

// 更新价格建立和审批
export function updatePrice(data) {
  return request({
    url: '/sms/price/update',
    method: 'put',
    data: data
  })
}

// 删除价格建立和审批
export function deletePrice(id) {
  return request({
    url: '/sms/price/delete?id=' + id,
    method: 'delete'
  })
}

// 获得价格建立和审批
export function getPrice(id) {
  return request({
    url: '/sms/price/get?id=' + id,
    method: 'get'
  })
}

// 获得价格建立和审批分页
export function getPricePage(params) {
  return request({
    url: '/sms/price/page',
    method: 'get',
    params
  })
}
// 导出价格建立和审批 Excel
export function exportPriceExcel(params) {
  return request({
    url: '/sms/price/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function auditPrice(id) {
  return request({
    url: '/sms/price/audit?id=' + id,
    method: 'get'
  })
}
export function cancelAuditPrice(id) {
  return request({
    url: '/sms/price/cancelAudit?id=' + id,
    method: 'get'
  })
}
export function getPricePage2(params) {
  return request({
    url: '/sms/price/page2',
    method: 'get',
    params
  })
}
