import request from '@/utils/request'

// 创建储位层级管理
export function createStorage(data) {
  return request({
    url: '/basic/storage/create',
    method: 'post',
    data: data
  })
}

// 更新储位层级管理
export function updateStorage(data) {
  return request({
    url: '/basic/storage/update',
    method: 'put',
    data: data
  })
}

// 删除储位层级管理
export function deleteStorage(id) {
  return request({
    url: '/basic/storage/delete?id=' + id,
    method: 'delete'
  })
}

// 获得储位层级管理
export function getStorage(id) {
  return request({
    url: '/basic/storage/get?id=' + id,
    method: 'get'
  })
}

// 获得储位层级管理分页
export function getStoragePage(params) {
  return request({
    url: '/basic/storage/page',
    method: 'get',
    params
  })
}
// 获得全部储位层级管理
export function getStorageList(params) {
  return request({
    url: '/basic/storage/list',
    method: 'get',
    params
  })
}

// 导出储位层级管理 Excel
export function exportStorageExcel(params) {
  return request({
    url: '/basic/storage/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function importTemplate() {
  return request({
    url: '/basic/storage/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}
