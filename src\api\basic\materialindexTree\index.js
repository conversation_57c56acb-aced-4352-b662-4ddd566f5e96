import request from '@/utils/request'

// 创建物料料号索引
export function createMaterialIndexTree(data) {
  return request({
    url: '/basic/material-index-tree/create',
    method: 'post',
    data: data
  })
}

// 更新物料料号索引
export function updateMaterialIndexTree(data) {
  return request({
    url: '/basic/material-index-tree/update',
    method: 'put',
    data: data
  })
}

// 删除物料料号索引
export function deleteMaterialIndexTree(id) {
  return request({
    url: '/basic/material-index-tree/delete?id=' + id,
    method: 'delete'
  })
}

// 获得物料料号索引
export function getMaterialIndexTree(id) {
  return request({
    url: '/basic/material-index-tree/get?id=' + id,
    method: 'get'
  })
}

// 获得物料料号索引列表
export function getMaterialIndexTreeList(params) {
  return request({
    url: '/basic/material-index-tree/list',
    method: 'get',
    params
  })
}
// 导出物料料号索引 Excel
export function exportMaterialIndexTreeExcel(params) {
  return request({
    url: '/basic/material-index-tree/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
