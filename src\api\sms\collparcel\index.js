import request from '@/utils/request'

// 创建收款单分配主档
export function createCollParcel(data) {
  return request({
    url: '/sms/coll-parcel/create',
    method: 'post',
    data: data
  })
}

// 更新收款单分配主档
export function updateCollParcel(data) {
  return request({
    url: '/sms/coll-parcel/update',
    method: 'put',
    data: data
  })
}

// 删除收款单分配主档
export function deleteCollParcel(id) {
  return request({
    url: '/sms/coll-parcel/delete?id=' + id,
    method: 'delete'
  })
}

// 获得收款单分配主档
export function getCollParcel(id) {
  return request({
    url: '/sms/coll-parcel/get?id=' + id,
    method: 'get'
  })
}

// 获得收款单分配主档分页
export function getCollParcelPage(params) {
  return request({
    url: '/sms/coll-parcel/page',
    method: 'get',
    params
  })
}
// 导出收款单分配主档 Excel
export function exportCollParcelExcel(params) {
  return request({
    url: '/sms/coll-parcel/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获得收款单分配主档
export function getTotalByCustNo(custNo) {
  return request({
    url: '/sms/coll-parcel/getTotalByCustNo?custNo=' + custNo,
    method: 'get'
  })
}

// ==================== 子表（收款单分配明细档） ====================
    // 获得收款单分配明细档分页
  export function getCollParcelDetailPage(params) {
    return request({
      url: '/sms/coll-parcel/coll-parcel-detail/page',
      method: 'get',
      params
    })
  }
        // 新增收款单分配明细档
  export function createCollParcelDetail(data) {
    return request({
      url: '/sms/coll-parcel/coll-parcel-detail/create',
      method: 'post',
      data
    })
  }
  // 修改收款单分配明细档
  export function updateCollParcelDetail(data) {
    return request({
      url: '/sms/coll-parcel/coll-parcel-detail/update',
      method: 'post',
      data
    })
  }
  // 删除收款单分配明细档
  export function deleteCollParcelDetail(id) {
    return request({
      url: '/sms/coll-parcel/coll-parcel-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得收款单分配明细档
  export function getCollParcelDetail(id) {
    return request({
      url: '/sms/coll-parcel/coll-parcel-detail/get?id=' + id,
      method: 'get'
    })
  }
