import request from '@/utils/request'

// 创建销售订单审批
export function createOrderAudit(data) {
  return request({
    url: '/sms/order-audit/create',
    method: 'post',
    data: data
  })
}

// 更新销售订单审批
export function updateOrderAudit(data) {
  return request({
    url: '/sms/order-audit/update',
    method: 'put',
    data: data
  })
}

// 删除销售订单审批
export function deleteOrderAudit(id) {
  return request({
    url: '/sms/order-audit/delete?id=' + id,
    method: 'delete'
  })
}

// 获得销售订单审批
export function getOrderAudit(id) {
  return request({
    url: '/sms/order-audit/get?id=' + id,
    method: 'get'
  })
}

// 获得销售订单审批分页
export function getOrderAuditPage(params) {
  return request({
    url: '/sms/order-audit/page',
    method: 'get',
    params
  })
}
// 导出销售订单审批 Excel
export function exportOrderAuditExcel(params) {
  return request({
    url: '/sms/order-audit/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
