import request from '@/utils/request'

// 创建代收代付主档
export function createCollectionPaymentMain(data) {
  return request({
    url: '/sms/collection-payment-main/create',
    method: 'post',
    data: data
  })
}

// 更新代收代付主档
export function updateCollectionPaymentMain(data) {
  return request({
    url: '/sms/collection-payment-main/update',
    method: 'put',
    data: data
  })
}

// 删除代收代付主档
export function deleteCollectionPaymentMain(id) {
  return request({
    url: '/sms/collection-payment-main/delete?id=' + id,
    method: 'delete'
  })
}

// 获得代收代付主档
export function getCollectionPaymentMain(id) {
  return request({
    url: '/sms/collection-payment-main/get?id=' + id,
    method: 'get'
  })
}

export function auditCollectionPayment(id) {
  return request({
    url: '/sms/collection-payment-main/auditCollectionPayment?id=' + id,
    method: 'get'
  })
}

export function collectionPaymentPushNC(id) {
  return request({
    url: '/sms/collection-payment-main/collectionPaymentPushNC?id=' + id,
    method: 'get'
  })
}
export function cancelAuditCollectionPayment(id) {
  return request({
    url: '/sms/collection-payment-main/cancelAuditCollectionPayment?id=' + id,
    method: 'get'
  })
}

// 获得代收代付主档分页
export function getCollectionPaymentMainPage(params) {
  return request({
    url: '/sms/collection-payment-main/page',
    method: 'get',
    params
  })
}

// 导出代收代付主档 Excel
export function exportCollectionPaymentMainExcel(params) {
  return request({
    url: '/sms/collection-payment-main/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（代收代付明细档） ====================
// 获得代收代付明细档分页
export function getCollectionPaymentDetailPage(params) {
  return request({
    url: '/sms/collection-payment-main/collection-payment-detail/page',
    method: 'get',
    params
  })
}

// 新增代收代付明细档
export function createCollectionPaymentDetail(data) {
  return request({
    url: '/sms/collection-payment-main/collection-payment-detail/create',
    method: 'post',
    data
  })
}

// 修改代收代付明细档
export function updateCollectionPaymentDetail(data) {
  return request({
    url: '/sms/collection-payment-main/collection-payment-detail/update',
    method: 'post',
    data
  })
}

// 删除代收代付明细档
export function deleteCollectionPaymentDetail(id) {
  return request({
    url: '/sms/collection-payment-main/collection-payment-detail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得代收代付明细档
export function getCollectionPaymentDetail(id) {
  return request({
    url: '/sms/collection-payment-main/collection-payment-detail/get?id=' + id,
    method: 'get'
  })
}

// 获得代收代付明细档
export function batchOperateDetail(data) {
  return request({
    url: '/sms/collection-payment-main/collection-payment-detail/batchOperateDetail',
    method: 'post',
    data
  })
}
