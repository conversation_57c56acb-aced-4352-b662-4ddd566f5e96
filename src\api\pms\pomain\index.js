import request from '@/utils/request'

// 创建合同信息
export function createPoMain(data) {
  return request({
    url: '/pms/po-main/create',
    method: 'post',
    data: data
  })
}

// 更新合同信息
export function updatePoMain(data) {
  return request({
    url: '/pms/po-main/update',
    method: 'put',
    data: data
  })
}

// 删除合同信息
export function deletePoMain(id) {
  return request({
    url: '/pms/po-main/delete?id=' + id,
    method: 'delete'
  })
}

// 获得合同信息
export function getPoMain(id) {
  return request({
    url: '/pms/po-main/get?id=' + id,
    method: 'get'
  })
}

// 获得合同信息分页
export function getPoMainPage(params) {
  return request({
    url: '/pms/po-main/page',
    method: 'get',
    params
  })
}

// 导出合同信息 Excel
export function exportPoMainExcel(params) {
  return request({
    url: '/pms/po-main/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 确认合同管理主表信息
export function continuePoMain(id) {
  return request({
    url: '/pms/po-main/continue?id=' + id,
    method: 'get'
  })
}

// ==================== 子表（合同明细） ====================
// 获得合同明细分页
export function getPoDetailPage(params) {
  return request({
    url: '/pms/po-main/po-detail/page',
    method: 'get',
    params
  })
}

// ==================== 子表（合同明细） ====================
// 获得合同明细分页
export function getPoDetailPage2(params) {
  return request({
    url: '/pms/po-main/po-detail/page2',
    method: 'get',
    params
  })
}

// 获得合同明细分页
export function getPoDetailPageConfirm(params) {
  return request({
    url: '/pms/po-main/po-detail/pageConfirm',
    method: 'get',
    params
  })
}

// 获得合同明细分页
export function getPoDetailPageY(params) {
  return request({
    url: '/pms/po-main/po-detail/pageY',
    method: 'get',
    params
  })
}

// 获得合同明细分页
export function getPoDetailPageFee(pono) {
  return request({
    url: '/pms/po-main/po-detail/pageFee?pono=' + pono,
    method: 'get'
  })
}

export function getPoDetailPageByParams(params) {
  return request({
    url: '/pms/po-main/po-detail/pageByParams',
    method: 'get',
    params
  })
}

export function getShipPoDetailPage(params) {
  return request({
    url: '/pms/po-main/po-detail/shipPage',
    method: 'get',
    params
  })
}

// 新增合同明细
export function createPoDetail(data) {
  return request({
    url: '/pms/po-main/po-detail/create',
    method: 'post',
    data
  })
}

// 修改合同明细
export function updatePoDetail(data) {
  return request({
    url: '/pms/po-main/po-detail/update',
    method: 'post',
    data
  })
}

// 删除合同明细
export function deletePoDetail(id) {
  return request({
    url: '/pms/po-main/po-detail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得合同明细
export function getPoDetail(id) {
  return request({
    url: '/pms/po-main/po-detail/get?id=' + id,
    method: 'get'
  })
}

// ==================== 子表（合同条款内容） ====================
// 获得合同条款内容分页
export function getPoTocPage(params) {
  return request({
    url: '/pms/po-main/po-toc/page',
    method: 'get',
    params
  })
}

// 新增合同条款内容
export function createPoToc(data) {
  return request({
    url: '/pms/po-main/po-toc/create',
    method: 'post',
    data
  })
}

// 修改合同条款内容
export function updatePoToc(data) {
  return request({
    url: '/pms/po-main/po-toc/update',
    method: 'post',
    data
  })
}

// 删除合同条款内容
export function deletePoToc(id) {
  return request({
    url: '/pms/po-main/po-toc/delete?id=' + id,
    method: 'delete'
  })
}

// 获得合同条款内容
export function getPoToc(id) {
  return request({
    url: '/pms/po-main/po-toc/get?id=' + id,
    method: 'get'
  })
}

// 按采购案项次批量新增
export function batchInsertDetails(data) {
  return request({
    url: '/pms/po-main/po-detail/batch',
    method: 'post',
    data
  })
}

// 按物料项次批量新增
export function batchInsertDetails2(data) {
  return request({
    url: '/pms/po-main/po-detail/batch2',
    method: 'post',
    data
  })
}

// 确认合同管理主表信息
export function createChildPo(id) {
  return request({
    url: '/pms/po-main/createChildPo?id=' + id,
    method: 'get'
  })
}

// 确认合同管理主表信息
export function endCasePo(id, flag) {
  return request({
    url: '/pms/po-main/endCase?id=' + id + '&flag=' + flag,
    method: 'get'
  })
}

// 获得付款信息
export function getPoPayInfo(params) {
  return request({
    url: '/pms/po-main/payInfo',
    method: 'get',
    params
  })
}

// 获得付款信息
export function getPoPayInfoY(params) {
  return request({
    url: '/pms/po-main/payInfoY',
    method: 'get',
    params
  })
}

// 获得付款信息
export function getShipmentsList(params) {
  return request({
    url: '/pms/insp-ship-main/shipments',
    method: 'get',
    params
  })
}

// 创建销售合同信息
export function createOrderInfo(data) {
  return request({
    url: '/pms/po-main/order/create',
    method: 'post',
    data: data
  })
}

// 更新销售合同信息
export function updateOrderInfo(data) {
  return request({
    url: '/pms/po-main/order/update',
    method: 'post',
    data: data
  })
}

// 获得销售合同信息
export function getOrderInfo(parentId) {
  return request({
    url: '/pms/po-main/order/get?parentId=' + parentId,
    method: 'get'
  })
}

// 创建销售合同信息
export function createOrderDetail(data) {
  return request({
    url: '/pms/po-main/order-detail/create',
    method: 'post',
    data: data
  })
}

// 更新销售合同信息
export function updateOrderDetail(data) {
  return request({
    url: '/pms/po-main/order-detail/update',
    method: 'post',
    data: data
  })
}

// 获得销售合同信息
export function getOrderDetail(parentId) {
  return request({
    url: '/pms/po-main/order-detail/get?parentId=' + parentId,
    method: 'get'
  })
}

// 获得销售合同信息
export function deleteOrderDetail(id) {
  return request({
    url: '/pms/po-main/order-detail/delete?id=' + id,
    method: 'get'
  })
}

// 获得销售合同明细分页
export function getOrderDetailPage(params) {
  return request({
    url: '/pms/po-main/order-detail/page',
    method: 'get',
    params
  })
}

// 按采购案项次批量新增
export function batchInsertDetailsInq(data) {
  return request({
    url: '/pms/po-main/po-detail/batchInq',
    method: 'post',
    data
  })
}

// 按采购案项次批量新增
export function batchInsertOrderDetails(data) {
  return request({
    url: '/pms/po-main/order-detail/batch',
    method: 'post',
    data
  })
}

export function getDetailPage(params) {
  return request({
    url: '/pms/po-main/po-detail/detailPage',
    method: 'get',
    params
  })
}
