import request from '@/utils/request'

// 创建采购计划调度
export function createMppDetailDispatch(data) {
  return request({
    url: '/pms/mpp-detail-dispatch/create',
    method: 'post',
    data: data
  })
}

// 更新采购计划调度
export function updateMppDetailDispatch(data) {
  return request({
    url: '/pms/mpp-detail-dispatch/update',
    method: 'put',
    data: data
  })
}

// 删除采购计划调度
export function deleteMppDetailDispatch(id) {
  return request({
    url: '/pms/mpp-detail-dispatch/delete?id=' + id,
    method: 'delete'
  })
}

// 获得采购计划调度
export function getMppDetailDispatch(id) {
  return request({
    url: '/pms/mpp-detail-dispatch/get?id=' + id,
    method: 'get'
  })
}

// 获得采购计划调度分页
export function getMppDetailDispatchPage(params) {
  return request({
    url: '/pms/mpp-detail-dispatch/page',
    method: 'get',
    params
  })
}
// 导出采购计划调度 Excel
export function exportMppDetailDispatchExcel(params) {
  return request({
    url: '/pms/mpp-detail-dispatch/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
//批量设置承办人（采购员）或者调度员
export function setrespempListMppDetailDispatch(data) {
  return request({
    url: '/pms/mpp-detail-dispatch/setrespempList',
    method: 'post',
    data
  })
}
export function setdispatchListMppDetailDispatch(data) {
  return request({
    url: '/pms/mpp-detail-dispatch/setdispatchList',
    method: 'post',
    data
  })
}
export function resetrespempListMppDetailDispatch(data) {
  return request({
    url: '/pms/mpp-detail-dispatch/resetrespempList',
    method: 'post',
    data
  })
}
export function resetdispatchListMppDetailDispatch(data) {
  return request({
    url: '/pms/mpp-detail-dispatch/resetdispatchList',
    method: 'post',
    data
  })
}
export function resetDispatch(data) {
  return request({
    url: '/pms/mpp-detail-dispatch/resetDispatch',
    method: 'put',
    data: data
  })
}
export function resetRespemp(data) {
  return request({
    url: '/pms/mpp-detail-dispatch/resetRespemp',
    method: 'put',
    data: data
  })
}
