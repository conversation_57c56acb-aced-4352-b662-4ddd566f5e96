// import axios from 'axios'
//
// export default {
//   getMonthlyPayments(startDate, endDate) {
//     return axios.get('/admin-api/pms/pomain/payments/monthly', {
//       params: { startDate, endDate }
//     })
//   },
//
//   getUpcomingPayments() {
//     return axios.get('/admin-api/pms/pomain/payments/upcoming')
//   },
//
//   markAsPaid(poNumber) {
//     return axios.post('/admin-api/pms/pomain/payments/mark-paid', { poNumber })
//   }
// }

import request from '@/utils/request'
// 月付款数据
export function getMonthlyPayments(params) {
  return request({
    url: '/pms/po-main/payments/monthly',
    method: 'get',
    params
  })
}

// 未来7天内即将付款合同
export function getUpcomingPayments(params) {
  return request({
    url: '/pms/po-main/payments/upcoming',
    method: 'get',
    params
  })
}

// 查询某天付款的合同
export function getDayPayInfo(params) {
  return request({
    url: '/pms/po-main/payments/dayInfo',
    method: 'get',
    params
  })
}
