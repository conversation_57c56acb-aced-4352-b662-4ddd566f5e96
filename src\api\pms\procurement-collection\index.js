import request from '@/utils/request'

// ==================== 主表相关接口 ====================

// 创建集采信息主表
export function createProcurementCollection(data) {
  return request({
    url: '/pms/procurement-collection/create',
    method: 'post',
    data: data
  })
}

// 更新集采信息主表
export function updateProcurementCollection(data) {
  return request({
    url: '/pms/procurement-collection/update',
    method: 'put',
    data: data
  })
}

// 删除集采信息主表
export function deleteProcurementCollection(id) {
  return request({
    url: '/pms/procurement-collection/delete?id=' + id,
    method: 'delete'
  })
}

// 获得集采信息主表
export function getProcurementCollection(id) {
  return request({
    url: '/pms/procurement-collection/get?id=' + id,
    method: 'get'
  })
}

// 获得集采信息主表分页
export function getProcurementCollectionPage(params) {
  return request({
    url: '/pms/procurement-collection/page',
    method: 'get',
    params
  })
}

// 导出集采信息主表 Excel
export function exportProcurementCollectionExcel(params) {
  return request({
    url: '/pms/procurement-collection/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表相关接口 ====================

// 创建集采物料明细
export function createProcurementCollectionItem(data) {
  return request({
    url: '/pms/procurement-collection/item/create',
    method: 'post',
    data: data
  })
}

// 更新集采物料明细
export function updateProcurementCollectionItem(data) {
  return request({
    url: '/pms/procurement-collection/item/update',
    method: 'put',
    data: data
  })
}

// 删除集采物料明细
export function deleteProcurementCollectionItem(id) {
  return request({
    url: '/pms/procurement-collection/item/delete?id=' + id,
    method: 'delete'
  })
}

// 获得集采物料明细
export function getProcurementCollectionItem(id) {
  return request({
    url: '/pms/procurement-collection/item/get?id=' + id,
    method: 'get'
  })
}

// 获得集采物料明细分页
export function getProcurementCollectionItemPage(params) {
  return request({
    url: '/pms/procurement-collection/item/page',
    method: 'get',
    params
  })
}

// 根据集采主表ID获取物料明细列表
export function getProcurementCollectionItemListByCollectionId(collectionId) {
  return request({
    url: '/pms/procurement-collection/item/list-by-collection-id?collectionId=' + collectionId,
    method: 'get'
  })
}

// 批量保存集采物料明细
export function batchSaveProcurementCollectionItems(collectionId, items, changeReason) {
  return request({
    url: '/pms/procurement-collection/item/batch-save?collectionId=' + collectionId + (changeReason ? '&changeReason=' + encodeURIComponent(changeReason) : ''),
    method: 'post',
    data: items
  })
}

// 根据集采主表ID和版本号获取物料明细列表
export function getProcurementCollectionItemListByCollectionIdAndVersion(collectionId, version) {
  return request({
    url: '/pms/procurement-collection/item/list-by-version?collectionId=' + collectionId + '&version=' + version,
    method: 'get'
  })
}

// ==================== 版本管理相关接口 ====================

// 获得版本记录分页
export function getProcurementCollectionItemVersionPage(params) {
  return request({
    url: '/pms/procurement-collection/version/page',
    method: 'get',
    params
  })
}

// 获取集采项目的所有版本号
export function getVersionsByCollectionId(collectionId) {
  return request({
    url: '/pms/procurement-collection/version/list-versions?collectionId=' + collectionId,
    method: 'get'
  })
}

// 根据集采主表ID和版本号获取版本记录列表
export function getVersionItemsByCollectionIdAndVersion(collectionId, versionNo) {
  return request({
    url: '/pms/procurement-collection/version/list-by-version?collectionId=' + collectionId + '&versionNo=' + versionNo,
    method: 'get'
  })
}
