import request from '@/utils/request'

// 创建储位料号实时库存
export function createWmsStockLoc(data) {
  return request({
    url: '/pms/wms-stock-loc/create',
    method: 'post',
    data: data
  })
}

// 更新储位料号实时库存
export function updateWmsStockLoc(data) {
  return request({
    url: '/pms/wms-stock-loc/update',
    method: 'put',
    data: data
  })
}

// 删除储位料号实时库存
export function deleteWmsStockLoc(id) {
  return request({
    url: '/pms/wms-stock-loc/delete?id=' + id,
    method: 'delete'
  })
}

// 获得储位料号实时库存
export function getWmsStockLoc(id) {
  return request({
    url: '/pms/wms-stock-loc/get?id=' + id,
    method: 'get'
  })
}

// 获得储位料号实时库存分页
export function getWmsStockLocPage(params) {
  return request({
    url: '/pms/wms-stock-loc/page',
    method: 'get',
    params
  })
}
// 导出储位料号实时库存 Excel
export function exportWmsStockLocExcel(params) {
  return request({
    url: '/pms/wms-stock-loc/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
