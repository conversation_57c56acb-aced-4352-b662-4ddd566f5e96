import request from '@/utils/request'

// 创建报关单据
export function createCustomsDeclaration(data) {
  return request({
    url: '/sms/customs-declaration/create',
    method: 'post',
    data: data
  })
}

// 更新报关单据
export function updateCustomsDeclaration(data) {
  return request({
    url: '/sms/customs-declaration/update',
    method: 'put',
    data: data
  })
}

// 删除报关单据
export function deleteCustomsDeclaration(id) {
  return request({
    url: '/sms/customs-declaration/delete?id=' + id,
    method: 'delete'
  })
}

// 获得报关单据
export function getCustomsDeclaration(id) {
  return request({
    url: '/sms/customs-declaration/get?id=' + id,
    method: 'get'
  })
}

// 获得报关单据分页
export function getCustomsDeclarationPage(params) {
  return request({
    url: '/sms/customs-declaration/page',
    method: 'get',
    params
  })
}
// 导出报关单据 Excel
export function exportCustomsDeclarationExcel(params) {
  return request({
    url: '/sms/customs-declaration/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（报关单据明细） ====================
      // 获得报关单据明细列表
    export function getCustomsDeclarationDetailListByParentid(parentid) {
      return request({
        url: '/sms/customs-declaration/customs-declaration-detail/list-by-parentid?parentid=' + parentid,
        method: 'get'
      })
    }
