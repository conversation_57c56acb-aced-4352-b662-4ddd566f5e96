import request from '@/utils/request'

// 创建费用估算
export function createEstimate(data) {
  return request({
    url: '/pms/estimate/create',
    method: 'post',
    data: data
  })
}

// 更新费用估算
export function updateEstimate(data) {
  return request({
    url: '/pms/estimate/update',
    method: 'put',
    data: data
  })
}

// 删除费用估算
export function deleteEstimate(id) {
  return request({
    url: '/pms/estimate/delete?id=' + id,
    method: 'delete'
  })
}

// 获得费用估算
export function getEstimate(id) {
  return request({
    url: '/pms/estimate/get?id=' + id,
    method: 'get'
  })
}

// 获得费用估算分页
export function getEstimatePage(params) {
  return request({
    url: '/pms/estimate/page',
    method: 'get',
    params
  })
}
// 导出费用估算 Excel
export function exportEstimateExcel(params) {
  return request({
    url: '/pms/estimate/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获得费用估算分页
export function continueEstimate(id) {
  return request({
    url: '/pms/estimate/confirm?id=' + id,
    method: 'get'
  })
}

// 获得费用估算分页
export function cancelEstimate(id) {
  return request({
    url: '/pms/estimate/cancel?id=' + id,
    method: 'get'
  })
}
