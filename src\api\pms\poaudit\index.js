import request from '@/utils/request'

// 创建合同审批
export function createPoAudit(data) {
  return request({
    url: '/pms/po-audit/create',
    method: 'post',
    data: data
  })
}

// 获得合同审批
export function getPoAudit(id) {
  return request({
    url: '/pms/po-audit/get?id=' + id,
    method: 'get'
  })
}

// 获得合同审批分页
export function getPoAuditPage(params) {
  return request({
    url: '/pms/po-audit/page',
    method: 'get',
    params
  })
}
