import request from '@/utils/request'

// 创建采购计划接办
export function createMppPurchaseDetail(data) {
  return request({
    url: '/pms/mpp-purchase-detail/create',
    method: 'post',
    data: data
  })
}

// 更新采购计划接办
export function updateMppPurchaseDetail(data) {
  return request({
    url: '/pms/mpp-purchase-detail/update',
    method: 'put',
    data: data
  })
}

// 删除采购计划接办
export function deleteMppPurchaseDetail(id) {
  return request({
    url: '/pms/mpp-purchase-detail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得采购计划接办
export function getMppPurchaseDetail(id) {
  return request({
    url: '/pms/mpp-purchase-detail/get?id=' + id,
    method: 'get'
  })
}

// 获得采购计划接办分页
export function getMppPurchaseDetailPage(params) {
  return request({
    url: '/pms/mpp-purchase-detail/page',
    method: 'get',
    params
  })
}
// 导出采购计划接办 Excel
export function exportMppPurchaseDetailExcel(params) {
  return request({
    url: '/pms/mpp-purchase-detail/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
export function takeover(data) {
  return request({
    url: '/pms/mpp-purchase-detail/takeover',
    method: 'post',
    data
  })
}
