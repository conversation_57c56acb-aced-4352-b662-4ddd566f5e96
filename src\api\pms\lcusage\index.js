import request from '@/utils/request'

// 创建信用证使用情况
export function createLcUsage(data) {
  return request({
    url: '/pms/lc-usage/create',
    method: 'post',
    data: data
  })
}

// 更新信用证使用情况
export function updateLcUsage(data) {
  return request({
    url: '/pms/lc-usage/update',
    method: 'put',
    data: data
  })
}

// 删除信用证使用情况
export function deleteLcUsage(id) {
  return request({
    url: '/pms/lc-usage/delete?id=' + id,
    method: 'delete'
  })
}

// 获得信用证使用情况
export function getLcUsage(id) {
  return request({
    url: '/pms/lc-usage/get?id=' + id,
    method: 'get'
  })
}

// 获得信用证使用情况分页
export function getLcUsagePage(params) {
  return request({
    url: '/pms/lc-usage/page',
    method: 'get',
    params
  })
}
// 导出信用证使用情况 Excel
export function exportLcUsageExcel(params) {
  return request({
    url: '/pms/lc-usage/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
