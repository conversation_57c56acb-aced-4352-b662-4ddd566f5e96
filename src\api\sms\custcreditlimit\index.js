import request from '@/utils/request'

// 创建客户授信额度维护
export function createCustCreditLimit(data) {
  return request({
    url: '/sms/cust-credit-limit/create',
    method: 'post',
    data: data
  })
}

// 更新客户授信额度维护
export function updateCustCreditLimit(data) {
  return request({
    url: '/sms/cust-credit-limit/update',
    method: 'put',
    data: data
  })
}

// 删除客户授信额度维护
export function deleteCustCreditLimit(id) {
  return request({
    url: '/sms/cust-credit-limit/delete?id=' + id,
    method: 'delete'
  })
}

// 获得客户授信额度维护
export function getCustCreditLimit(id) {
  return request({
    url: '/sms/cust-credit-limit/get?id=' + id,
    method: 'get'
  })
}

// 获得客户授信额度维护分页
export function getCustCreditLimitPage(params) {
  return request({
    url: '/sms/cust-credit-limit/page',
    method: 'get',
    params
  })
}
// 导出客户授信额度维护 Excel
export function exportCustCreditLimitExcel(params) {
  return request({
    url: '/sms/cust-credit-limit/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function effectiveCustCreditLimit(id) {
  return request({
    url: '/sms/cust-credit-limit/effective?id=' + id,
    method: 'get'
  })
}
export function invalidCustCreditLimit(id) {
  return request({
    url: '/sms/cust-credit-limit/invalid?id=' + id,
    method: 'get'
  })
}
