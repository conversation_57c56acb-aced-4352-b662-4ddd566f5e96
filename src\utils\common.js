import store from '@/store'

export const PLATE_TYPE_CODE = [1, 4]//类型为板块的属性值
export const COMP_TYPE_CODE = [1, 3, 4, 6, 11]//类型为单位的属性值
export const COMP_HDEPT_TYPE_CODE = [1, 2, 3, 5, 4, 6, 11]//类型为单位,和高级职能部室的属性值
export const DEPT_TYPE_CODE = [2, 5, 7, 8, 12, 13]//类型为部门的属性值
//边姐专用
export const LWBM_TYPE_CODE = [16]//类型为劳务部门
export const LWBM_DEPT_TYPE_CODE = [...DEPT_TYPE_CODE, ...LWBM_TYPE_CODE]

/**
 * 判断是否是集团系统
 * @returns {boolean}
 */
export function isJt() {
  return store.state.user.compid === 0
}

/**
 * 判断是否是股份系统
 * @returns {boolean}
 */
export function isGf() {
  return store.state.user.compid === 1
}

/**
 * 判断是否是稀土系统
 * @returns {boolean}
 */
export function isXt() {
  return store.state.user.compid === 2
}

/**
 * 判断登录的用户是否是集团安全管理部
 * @returns {boolean}
 */
export function isJtSafeDept(deptid = store.state.user.deptid) {
  return [100, 6852].includes(Number(deptid))
}

/**
 * 判断登录的用户是否是集团安全管理部
 * @returns {boolean}
 */
export function isGfSafeDept(deptid = store.state.user.deptid) {
  return [101, 354].includes(Number(deptid))
}

/**
 * 根据 部门获取单位
 */
export function getDwByDeptId(dId, listDept = []) {
  if (dId == null) {
    return {};
  }
  let result = listDept.find(item => item.value == dId).ancestors.split(',')
  if (result.length >= 4) {
    return listDept.find(item => item.value == result[3]) || {}
  } else {
    return listDept.find(item => item.value == dId) || {}
  }
}

/**
 * 根据 部门以下获取部门
 */
export function getBmByDeptId(dId, listDept = []) {
  if (dId == null) {
    return {};
  }
  let result = listDept.find(item => item.value == dId).ancestors.split(',')
  if (result.length >= 5) {
    return listDept.find(item => item.value == result[4]) || {}
  } else {
    return listDept.find(item => item.value == dId) || {}
  }
}

/**
 * 判断日期是否过期
 */
export function isTimeout(date) {
  return new Date(date).getTime() - Date.now() <= 0
}

/**
 * 将数组结构转为树结构
 */
export function convertToTree(arr, selfIdField = 'tid', parentIdField = 'pid') {
  const map = {};
  const tree = [];

  arr.forEach((node) => {
    node.children = undefined
    map[node[selfIdField]] = node;
  });

  arr.forEach((node) => {
    if (node[parentIdField] !== null && node[parentIdField] !== 0 && typeof map[node[parentIdField]] !== 'undefined') {
      const parent = map[node[parentIdField]];
      if (Array.isArray(parent.children)) {
        parent.children.push(node);
      } else {
        parent.children = []
        parent.children.push(node);
      }
    } else {
      tree.push(node);
    }
  });

  return tree;
}

/**
 * 将数组结构转为树结构, 通过子节点id构建
 * @param data
 * @param selfIdField
 * @param childIdsField
 * @returns {*[]}
 */
export function convertToTreeByChildIds(data, selfIdField = 'tid', childIdsField = 'childIds') {
  // 创建一个 id -> 节点的映射表
  const nodeMap = {};
  data.forEach(item => {
    nodeMap[item[selfIdField]] = {...item};
  });

  // 构建树形结构
  const tree = [];
  data.forEach(item => {
    const parentNode = nodeMap[item[selfIdField]];
    if (item[childIdsField]) {
      const childIds = item[childIdsField].split(',');
      const children = childIds
        .map(childId => nodeMap[childId])
        .filter(childNode => childNode);

      if (children.length > 0) {
        parentNode.children = children;
      }
    }

    if (!data.some(d => d[childIdsField] && d[childIdsField].split(',').includes(item[selfIdField].toString()))) {
      tree.push(parentNode);
    }
  });

  return tree;
}

//根据id获取对应节点
const findNodeInSubtree = (root, id) => {
  if (String(root.id) === String(id)) {
    return root;
  }
  if (!Array.isArray(root.children)) return undefined
  for (const child of root.children) {
    const foundNode = findNodeInSubtree(child, id);
    if (foundNode) {
      return foundNode;
    }
  }
  return undefined;
};

//根据id获取对应节点父级节点
const findParentInSubtree = (root, id) => {
  if (!Array.isArray(root.children)) return undefined
  if (root.children.some(child => child.id === id)) {
    return root;
  }
  for (const child of root.children) {
    const parentNode = findParentInSubtree(child, id);
    if (parentNode) {
      return parentNode;
    }
  }

  return null;
};

//根据id获取对应节点, 返回数组
export const findTreeNodeById = (roots = [], id) => {
  for (const root of roots) {
    const foundNode = findNodeInSubtree(root, id);
    if (foundNode) {
      return [foundNode];
    }
  }
  return [];
};

//根据id获取对应父节点, 返回数组
export const findParentNodeById = (roots = [], id) => {
  for (const root of roots) {
    const foundNode = findParentInSubtree(root, id);
    if (foundNode) {
      return [foundNode];
    }
  }
  return [];
};

/**
 * 根据用户deptid获取对应部门列表
 */
const special = [354]//需要特殊处理的部门id, 如股份安全管理部
export const findDeptNode = (deptOptions) => {
  const deptId = store.state.user.deptid
  return isGfSafeDept() ? findParentNodeById(deptOptions, deptId) : findTreeNodeById(deptOptions, deptId)
}


//递归过滤树节点
function filterTreeNodesRecursive(tree, callback) {
  const children = tree.children ? tree.children.map(child => filterTreeNodesRecursive(child, callback)).filter(child => child !== null) : [];
  if (callback(tree) || children.length > 0) {
    const filteredTree = {...tree};
    if (children.length > 0) {
      filteredTree.children = children;
    } else {
      delete filteredTree.children;
    }
    return filteredTree;
  }

  return null;
}

/**
 * 根据callback返回条件过滤树结构
 */
export function filterTreeNodes(trees, callback) {
  return trees.reduce((acc, tree) => {
    const filteredTree = filterTreeNodesRecursive(tree, callback);
    if (filteredTree !== null) {
      acc.push(filteredTree);
    }
    return acc;
  }, []);
}

/**
 * 过滤deptTree, 只剩下部门
 */
export function deptDeptTreeFilter(deptOptions) {
  return filterTreeNodes(deptOptions, tree => DEPT_TYPE_CODE.includes(tree.remark11))
}

/**
 * 边姐专用
 * 过滤deptTree, 只剩下劳务部门
 */
export function lwbmDeptTreeFilter(deptOptions) {
  return filterTreeNodes(deptOptions, tree => LWBM_TYPE_CODE.includes(tree.remark11))
}

/**
 * 边姐专用
 * 过滤deptTree, 只剩下劳务部门和部门
 */
export function lwbmAndBmDeptTreeFilter(deptOptions) {
  return filterTreeNodes(deptOptions, tree => LWBM_DEPT_TYPE_CODE.includes(tree.remark11))
}

/**
 * 过滤deptTree, 只剩下单位
 */
export function compDeptTreeFilter(deptOptions) {
  return filterTreeNodes(deptOptions, tree => COMP_TYPE_CODE.includes(tree.remark11))
}

/**
 * 过滤deptTree, 只剩下单位和高级职能部室
 */
export function compHdeptDeptTreeFilter(deptOptions) {
  return filterTreeNodes(deptOptions, tree => COMP_HDEPT_TYPE_CODE.includes(tree.remark11))
}

/**
 * 过滤deptTree, 只剩下板块
 */
export function plateDeptTreeFilter(deptOptions) {
  return filterTreeNodes(deptOptions, tree => PLATE_TYPE_CODE.includes(tree.remark11))
}

/**
 * 解析身份证号中的生日,性别,年龄
 */
export function analysisIdCard(idCard) {
  // 身份证号码验证正则表达式
  const regex = /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/;

  // 验证身份证号码格式
  if (!regex.test(idCard)) {
    return {};
  }

  // 提取出生年月日和性别信息
  const year = idCard.substring(6, 10);
  const month = idCard.substring(10, 12);
  const day = idCard.substring(12, 14);
  const sexCode = idCard.substring(16, 17);

  // 获取当前日期
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth() + 1;
  const currentDay = today.getDate();

  // 计算年龄
  let age = currentYear - parseInt(year);
  if (parseInt(month) > currentMonth || (parseInt(month) === currentMonth && parseInt(day) > currentDay)) {
    age--;
  }

  // 判断性别
  const sex = sexCode % 2 === 0 ? '女' : '男';

  // 格式化出生日期
  const birth = `${year}-${month}-${day}`;

  // 返回解析结果
  return {age, sex, birth};
}

//更具回调函数条件返回对应节点的全部父级节点(包含自身)
const findParentNodes = (tree, nodeCallback, parents = []) => {
  for (const node of tree) {
    if (nodeCallback(node)) {
      return [...parents, node];
    }
    if (node.children) {
      const found = findParentNodes(node.children, nodeCallback, [...parents, node]);
      if (found.length > 0) {
        return found;
      }
    }
  }
  return [];
};

export const findFilterParent = (tree, nodeCallback, parentCallback) => {
  const results = findParentNodes(tree, nodeCallback).filter(parentCallback)
  return results.length <= 1 ? results : [results[results.length - 1]]
}
/**
 * 根据deptId获取对应符合属性为部门或单位的最深层父级节点
 * 返回数组
 */
export const findCompOrDeptParentNode = (deptOptions, deptId) => {
  return findFilterParent(deptOptions,
    node => String(node.id) === String(deptId),
    parent => [...COMP_TYPE_CODE, ...DEPT_TYPE_CODE].includes(parent.remark11)
  )
}

/**
 * 根据deptId获取对应符合属性为单位的最深层父级节点
 * 返回数组
 */
export const findCompParentNode = (deptOptions, deptId) => {
  return findFilterParent(deptOptions,
    node => String(node.id) === String(deptId),
    parent => [...COMP_TYPE_CODE].includes(parent.remark11)
  )
}
/**
 * 根据deptId获取对应符合属性为部门的最深层父级节点
 * 返回数组
 */
export const findDeptParentNode = (deptOptions, deptId) => {
  return findFilterParent(deptOptions,
    node => String(node.id) === String(deptId),
    parent => [...DEPT_TYPE_CODE].includes(parent.remark11)
  )
}

/**
 * 根据deptId获取对应符合属性为单位的最深层父级节点
 * 返回对象
 */
export const findCompParentObj = (deptOptions, deptId) => {
  return findCompParentNode(deptOptions, deptId)[0] || {id: '', label: ''}
}
/**
 * 根据deptId获取对应符合属性为部门的最深层父级节点
 * 返回对象
 */
export const findDeptParentObj = (deptOptions, deptId) => {
  return findDeptParentNode(deptOptions, deptId)[0] || {id: '', label: ''}
}

/**
 * 根据部门id判断该部门节点是否是部门
 */
export const isDept = (deptOptions, deptId) => {
  const deptNode = findTreeNodeById(deptOptions, deptId)[0] || {}
  return DEPT_TYPE_CODE.includes(deptNode.remark11)
}

/**
 * 根据部门id判断该部门节点是否是单位
 */
export const isComp = (deptOptions, deptId) => {
  const deptNode = findTreeNodeById(deptOptions, deptId)[0] || {}
  return COMP_TYPE_CODE.includes(deptNode.remark11)
}

/**
 * 边姐专专用
 * 根据部门id判断该部门节点是否是劳务部门
 */
export const isLwbm = (deptOptions, deptId) => {
  const deptNode = findTreeNodeById(deptOptions, deptId)[0] || {}
  return LWBM_TYPE_CODE.includes(deptNode.remark11)
}

/**
 * 根据字典选项找字典
 */
export const findDict = (dictOptions, dictValue) => {
  const goal = dictOptions.find(item => item.value === dictValue)
  return goal ? goal : {}
}

/**
 * 将过长的字符串进行换行分割
 * @param label 要分割的字符串
 * @param sliceCount 一行最多显示的文字
 * @returns {string}
 */
export function formatLongString(label, sliceCount = 5) {
  let result = '';
  for (let i = 0; i < label.length; i += sliceCount) {
    result += label.substr(i, sliceCount) + '\n';
  }
  return result;
}

/**
 * 根据id获取多选项label
 */
export function getMulLabel(values, options) {
  if (!values) return ''
  const valueArr = Array.isArray(values) ? values : values.split(',')
  return valueArr.map(value => {
    const goal = options.find(op => String(op.value) === String(value)) || {}
    return goal.label
  }).join(',')
}

/**
 * 多选树形结构数据获取多选labels
 * @param idsParams
 * @param treeData
 * @param idField
 * @param labelField
 * @param childrenField
 * @returns {string}
 */
export const findTreeLabels = (idsParams,
                               treeData = [],
                               idField = 'id',
                               labelField = 'label',
                               childrenField = 'children') => {
  const labels = []; // 用于存储找到的labels
  const ids = typeof idsParams === 'string' && idsParams ? idsParams.split(',') : []

  const findLabelsRecursively = (nodes) => {
    nodes.forEach(node => {
      if (ids.includes(String(node[idField]))) {
        labels.push(node[labelField]); // 找到节点，加入label数组
      }
      // 如果节点有子节点，递归查找
      if (node[childrenField] && node[childrenField].length > 0) {
        findLabelsRecursively(node[childrenField]);
      }
    });
  };

  findLabelsRecursively(treeData); // 使用递归函数开始递归搜索
  return labels.join(','); // 返回收集到的labels数组
};

/**
 * 根据id过滤对应节点及其下级节点
 * @param nodes
 * @param idToRemove
 * @param idField
 * @returns {*}
 */
export function filterTreeAndChildren(nodes, idToRemove, idField = 'id') {
  return nodes.reduce((acc, node) => {
    if (String(node[idField]) === String(idToRemove)) {
      return acc;
    }

    const newNode = {...node};

    if (node.children && node.children.length > 0) {
      const filteredChildren = filterTreeAndChildren(node.children, idToRemove);
      if (filteredChildren.length > 0) {
        newNode.children = filteredChildren;
      } else {
        delete newNode.children; // 如果过滤后没有子节点，删除 children 属性
      }
    }

    acc.push(newNode);
    return acc;
  }, []);
}

/**
 * 根据某个字段去重数组
 * @param arr
 * @param field
 * @returns {*}
 */
export function removeDuplicates(arr, field) {
  const seen = new Set();
  return arr.filter((item) => {
    if (seen.has(item[field])) {
      return false;
    }
    seen.add(item[field]);
    return true;
  });
}

/**
 * 根据数组对象中的某个字段分组
 * @param array 原数组
 * @param field 字段
 * @returns {*[]}
 */
export function groupByField(array = [], field = 'field') {
  const groupList = []
  array.forEach((item) => {
    const goalIndex = groupList.findIndex(
      (el) => typeof item[field] !== 'undefined' && el[field] === item[field]
    )
    if (goalIndex === -1) {
      groupList.push({
        [field]: item[field],
        list: [item],
      })
    } else {
      groupList[goalIndex].list.push(item)
    }
  })
  return groupList
}

/**
 * 保留n位小数
 * @param num
 * @param n
 * @returns {number}
 */
export function keepDecimal(num, n) {
  const multiplier = Math.pow(10, n);
  const roundedNum = Math.round(num * multiplier);
  return roundedNum / multiplier;
}

/**
 * 递归遍历树型结构数据, 并每次遍历执行回调函数中的内容
 * @param tree 树型结构数据
 * @param nodeCallback 回调函数
 * @constructor
 */
export const travelTree = (tree, nodeCallback) => {
  for (const node of tree) {
    nodeCallback(node)
    Array.isArray(node.children) && travelTree(node.children, nodeCallback);
  }
};

/**
 * 用*隐藏部分字段
 * @param text
 * @param start
 * @param end
 * @returns {*}
 */
export function hiddenPartData(text, start = 0, end = 0) {
  if (!text || text.length < end - start) return text
  return text.slice(0, start) + '*'.repeat(end - start) + text.slice(end)
}

/**
 * 用*隐藏手机号
 * @param phoneNumber
 * @returns {*}
 */
export function hiddenPartPhone(phoneNumber) {
  return hiddenPartData(phoneNumber, 3, 7)
}

/**
 * 用*隐藏身份证号
 * @param idCard
 * @returns {*}
 */
export function hiddenPartIdCard(idCard) {
  return hiddenPartData(idCard, 4, 15)
}

/**
 * 获取树形结构数据的全部父级节点,包括自身
 * @param options
 * @param id
 * @param idField
 * @param childrenField
 * @returns {T[]|*[]}
 */
export const getParentNodes = (options, id, idField = 'id', childrenField = 'children') => {
  for (const item of options) {
    if (String(item[idField]) === String(id)) {
      return [item]
    }
    if (item[childrenField]) {
      let node = getParentNodes(item.children, id, idField, childrenField);
      if (typeof node !== 'undefined') {
        return node.concat(item)
      }
    }
  }
}

/**
 * 获取树形结构label完整路径
 * @param id
 * @param treeData
 * @param idField
 * @param labelField
 * @param childrenField
 */
export function findTreeLabel(id, treeData = [], idField = 'id', labelField = 'label', childrenField = 'children') {
  const nodes = getParentNodes(treeData, id, idField, childrenField)
  nodes.reverse()
  return nodes.map(item => item[labelField]).join('/')
}

/**
 * 判断是否为空值
 * @param value
 */
export function isEmptyValue(value) {
  return typeof value === 'undefined' || value === null || value === ''
}


/**
 * 比较两个变量大小, 返回1,0,-1 用于排序
 * @param a
 * @param b
 * @returns {number}
 */
export function compareData(a, b) {
  if (a < b) return -1
  if (a === b) return 0
  if (a > b) return 1
}

/**
 * 离散日期格式化显示
 * @param dates
 */
export function formatDates(dates) {
  if (!dates) return ''
  // 将日期字符串转换为 moment 对象
  dates = dates.split(',').map(date => moment(date));
  // 对日期进行排序
  dates.sort((a, b) => a - b);

  let result = [];
  let start = dates[0];
  let end = dates[0];

  for (let i = 1; i < dates.length; i++) {
    if (dates[i].diff(end, 'days') === 1) { // 如果日期是连续的（相差一天）
      end = dates[i];
    } else {
      // 结束当前区间并开始新的区间
      if (start.isSame(end, 'day')) {
        result.push(start.format('YYYY-MM-DD'));
      } else {
        result.push(`${start.format('YYYY-MM-DD')}至${end.format('YYYY-MM-DD')}`);
      }
      start = dates[i];
      end = dates[i];
    }
  }

  // 添加最后一个区间
  if (start.isSame(end, 'day')) {
    result.push(start.format('YYYY-MM-DD'));
  } else {
    result.push(`${start.format('YYYY-MM-DD')}至${end.format('YYYY-MM-DD')}`);
  }

  return result.join(' , ');
}

/**
 * 获取图片分辨率
 * @param file
 * @returns {Promise<unknown>}
 */
export function getImageResolution(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (e) => {
      const img = new Image();

      img.onload = () => {
        const width = img.width;
        const height = img.height;
        resolve({width, height});
      };

      img.onerror = () => {
        reject(new Error('无法加载图片'));
      };

      img.src = e.target.result;
    };

    reader.onerror = () => {
      reject(new Error('无法读取文件'));
    };

    reader.readAsDataURL(file);
  });
}

/**
 * 判断是否是JSON格式的字符串
 * @param value
 * @returns {boolean}
 */
export function isJSONStr(value) {
  if (typeof value !== 'string') {
    return false; // 如果值不是字符串，直接返回false
  }

  try {
    const parsed = JSON.parse(value);
    return typeof parsed === 'object' && parsed !== null;
  } catch (e) {
    return false; // 如果解析失败，返回false
  }
}

/**
 * 根据url获取文件名
 * @returns {*|string}
 * @param url
 */
export function getFileName(url) {
  if (!url) return ''
  return url.slice(url.lastIndexOf("/") + 1);
}

/**
 * 将多选项中某个字段合并去重, 返回用,拼起来的数据
 * @param selected
 * @param field
 */
export function mergeMulSelect(selected, field) {
  if (!Array.isArray(selected) || selected.label <= 0 || !field) return ''
  return [...new Set(selected.map(item => item[field]).join(',').split(','))].join(',')
}

/**
 * 将驼峰命名转化为下划线命名
 * @param str
 * @returns {string}
 */
export function camelToUnderscore(str) {
  // 使用正则表达式将大写字母前插入下划线，并将整个字符串转换为小写
  return str.replace(/([A-Z])/g, '_$1') // 处理大写字母
    .replace(/([a-zA-Z])([0-9])/g, '$1_$2') // 处理字母和数字之间的分隔
    .toLowerCase() // 转换为小写
    .replace(/^_+|_+$/g, ''); // 去除开头和结尾的下划线
}

/**
 * 为表单赋值, 附上创建时间和创建人
 * @param form
 */
export function setCUD(form) {
  form.cUser = store.state.user.userid
  form.cTime = new Date()
}

/**
 * 为表单赋值, 附上修改时间和修改人
 * @param form
 */
export function setUUD(form) {
  form.uUser = store.state.user.userid
  form.uTime = new Date()
}

/**
 * 正则配置
 */
export const regular = {
  image: /\.(png|jpg|gif|jpeg|webp|jfif|pjepg|pjp)$/i,
  phone: /^1[2-9]\d{9}$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{6,20}$/,
  IP: /^((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)$/,
  idCard:
    /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  positiveNumber: /^((1000000000)|(\d{1,9}))$/, // 非负
  number: /^-?((1000000000)|(\d{1,9}))$/,
  email: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/, // 判断是否是邮箱
  fileLength: /^.{1,100}$/, //判断文件名是否超长
  space: /^[^\s]*$/,
  positiveInteger: /^[1-9]\d*$/,
  nonnegativeInteger: /^(0|[1-9]\d*)$/,
}
export const message = {
  image: '只能上传图片',
  phone: '手机号格式有误',
  password: '密码至少包含大小写字母、数字和特殊字符，长度为6-20位',
  IP: 'ip地址格式有误',
  isUploading: '正在上传文件，请稍后',
  idCard: '身份证号格式有误',
  positiveNumber: '正数最大长度为11位',
  number: '整数最大长度为11位',
  field: '内容最大长度为50',
  nonEmptyField: '内容无空格且最大长度为50',
  textField: '内容最大长度为200',
  email: '请输入正确的邮箱',
  fileLength: '文件名过长',
  space: '请勿输入空格',
  positiveInteger: '请输入正整数',
  nonnegativeInteger: '请输入非负整数',
}

/**
 * 获取移动端链接
 * @param compid
 * @returns {*}
 */
export function getMobileURL(compid = store.state.user.compid) {
  return {
    '0': 'https://gfaq-e.nmxl.com.cn/jt',
    '1': 'https://gfaq-e.nmxl.com.cn/gf',
    '2': 'https://gfaq-e.nmxl.com.cn/xt',
    '3999': 'https://gfaq-e.nmxl.com.cn/aq'
  }[compid]
}
