import request from '@/utils/request'

// 创建物料申请
export function createMaterialApply(data) {
  return request({
    url: '/basic/material-apply/create',
    method: 'post',
    data: data
  })
}

// 更新物料申请
export function updateMaterialApply(data) {
  return request({
    url: '/basic/material-apply/update',
    method: 'put',
    data: data
  })
}

// 删除物料申请
export function deleteMaterialApply(id) {
  return request({
    url: '/basic/material-apply/delete?id=' + id,
    method: 'delete'
  })
}

// 获得物料申请
export function getMaterialApply(id) {
  return request({
    url: '/basic/material-apply/get?id=' + id,
    method: 'get'
  })
}

// 获得物料申请分页
export function getMaterialApplyPage(params) {
  return request({
    url: '/basic/material-apply/page',
    method: 'get',
    params
  })
}
// 导出物料申请 Excel
export function exportMaterialApplyExcel(params) {
  return request({
    url: '/basic/material-apply/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
