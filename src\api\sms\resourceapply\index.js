import request from '@/utils/request'

// 创建产品资源申请
export function createResourceApply(data) {
  return request({
    url: '/sms/resource-apply/create',
    method: 'post',
    data: data
  })
}

// 更新产品资源申请
export function updateResourceApply(data) {
  return request({
    url: '/sms/resource-apply/update',
    method: 'put',
    data: data
  })
}

// 删除产品资源申请
export function deleteResourceApply(id) {
  return request({
    url: '/sms/resource-apply/delete?id=' + id,
    method: 'delete'
  })
}

// 获得产品资源申请
export function getResourceApply(id) {
  return request({
    url: '/sms/resource-apply/get?id=' + id,
    method: 'get'
  })
}

// 获得产品资源申请分页
export function getResourceApplyPage(params) {
  return request({
    url: '/sms/resource-apply/page',
    method: 'get',
    params
  })
}
// 导出产品资源申请 Excel
export function exportResourceApplyExcel(params) {
  return request({
    url: '/sms/resource-apply/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 生效流程
export function effectiveResourceApply(id) {
  return request({
    url: '/sms/resource-apply/effect?id=' + id,
    method: 'get'
  })
}

// 取消生效
export function invalidResourceApply(id) {
  return request({
    url: '/sms/resource-apply/invalid?id=' + id,
    method: 'get'
  })
}

// ==================== 子表（产品资源申请明细） ====================
    // 获得产品资源申请明细分页
  export function getResourceApplyDetailPage(params) {
    return request({
      url: '/sms/resource-apply/resource-apply-detail/page',
      method: 'get',
      params
    })
  }
        // 新增产品资源申请明细
  export function createResourceApplyDetail(data) {
    return request({
      url: '/sms/resource-apply/resource-apply-detail/create',
      method: 'post',
      data
    })
  }
  // 修改产品资源申请明细
  export function updateResourceApplyDetail(data) {
    return request({
      url: '/sms/resource-apply/resource-apply-detail/update',
      method: 'post',
      data
    })
  }
  // 删除产品资源申请明细
  export function deleteResourceApplyDetail(id) {
    return request({
      url: '/sms/resource-apply/resource-apply-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得产品资源申请明细
  export function getResourceApplyDetail(id) {
    return request({
      url: '/sms/resource-apply/resource-apply-detail/get?id=' + id,
      method: 'get'
    })
  }
