import request from '@/utils/request'

export function getProcessDefinitionPage(query) {
  return request({
    url: '/bpm/process-definition/page',
    method: 'get',
    params: query
  })
}


export async function getProcessDefinition(id) {
  return await request({
    url: '/bpm/process-definition/get?id=' + id,
    method: 'get'
  })
}

export function getProcessDefinitionList(query) {
  return request({
    url: '/bpm/process-definition/list',
    method: 'get',
    params: query
  })
}

export async function getProcessDefinitionBpmnXML(id) {
  return await request({
    //url: '/bpm/process-definition/get-bpmn-xml?id=' + id,
    url: '/bpm/process-definition/get?id=' + id,
    method: 'get'
  })
}
