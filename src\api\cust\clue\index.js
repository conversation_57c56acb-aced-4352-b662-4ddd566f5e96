import request from '@/utils/request'

// 创建线索
export function createClue(data) {
  return request({
    url: '/cust/clue/create',
    method: 'post',
    data: data
  })
}

// 更新线索
export function updateClue(data) {
  return request({
    url: '/cust/clue/update',
    method: 'put',
    data: data
  })
}

// 删除线索
export function deleteClue(id) {
  return request({
    url: '/cust/clue/delete?id=' + id,
    method: 'delete'
  })
}

// 获得线索
export function getClue(id) {
  return request({
    url: '/cust/clue/get?id=' + id,
    method: 'get'
  })
}

// 获得线索分页
export function getCluePage(params) {
  return request({
    url: '/cust/clue/page',
    method: 'get',
    params
  })
}
// 导出线索 Excel
export function exportClueExcel(params) {
  return request({
    url: '/cust/clue/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
