import request from '@/utils/request'

// 创建配置信息
export function createParamsInfo(data) {
  return request({
    url: '/pms/params-info/create',
    method: 'post',
    data: data
  })
}

// 更新配置信息
export function updateParamsInfo(data) {
  return request({
    url: '/pms/params-info/update',
    method: 'put',
    data: data
  })
}

// 删除配置信息
export function deleteParamsInfo(id) {
  return request({
    url: '/pms/params-info/delete?id=' + id,
    method: 'delete'
  })
}

// 获得配置信息
export function getParamsInfo(id) {
  return request({
    url: '/pms/params-info/get?id=' + id,
    method: 'get'
  })
}

// 获得配置信息分页
export function getParamsInfoPage(params) {
  return request({
    url: '/pms/params-info/page',
    method: 'get',
    params
  })
}
export function getParamsInfoList(params) {
  return request({
    url: '/pms/params-info/list',
    method: 'get',
    params
  })
}
// 导出配置信息 Excel
export function exportParamsInfoExcel(params) {
  return request({
    url: '/pms/params-info/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
