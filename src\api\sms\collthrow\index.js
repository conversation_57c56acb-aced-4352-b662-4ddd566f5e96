import request from '@/utils/request'

// 创建收款单抛账档
export function createCollThrow(data) {
  return request({
    url: '/sms/coll-throw/create',
    method: 'post',
    data: data
  })
}

// 更新收款单抛账档
export function updateCollThrow(data) {
  return request({
    url: '/sms/coll-throw/update',
    method: 'put',
    data: data
  })
}

// 删除收款单抛账档
export function deleteCollThrow(id) {
  return request({
    url: '/sms/coll-throw/delete?id=' + id,
    method: 'delete'
  })
}

// 获得收款单抛账档
export function getCollThrow(id) {
  return request({
    url: '/sms/coll-throw/get?id=' + id,
    method: 'get'
  })
}

// 获得收款单抛账档分页
export function getCollThrowPage(params) {
  return request({
    url: '/sms/coll-throw/page',
    method: 'get',
    params
  })
}
// 导出收款单抛账档 Excel
export function exportCollThrowExcel(params) {
  return request({
    url: '/sms/coll-throw/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
