import request from '@/utils/request'

// 创建配船明细
export function createShippingDetail(data) {
  return request({
    url: '/sms/shipping-detail/create',
    method: 'post',
    data: data
  })
}

// 更新配船明细
export function updateShippingDetail(data) {
  return request({
    url: '/sms/shipping-detail/update',
    method: 'put',
    data: data
  })
}

// 删除配船明细
export function deleteShippingDetail(id) {
  return request({
    url: '/sms/shipping-detail/delete?id=' + id,
    method: 'delete'
  })
}

export function handleAudit(id) {
  return request({
    url: '/sms/shipping-detail/handleAudit?id=' + id,
    method: 'get'
  })
}

export function cancelAudit(id) {
  return request({
    url: '/sms/shipping-detail/cancelAudit?id=' + id,
    method: 'get'
  })
}

export function customsDeclaration(id) {
  return request({
    url: '/sms/shipping-detail/customsDeclaration?id=' + id,
    method: 'get'
  })
}

export function writeoff(id) {
  return request({
    url: '/sms/shipping-detail/writeoff?id=' + id,
    method: 'get'
  })
}
export function antiwriteoff(id) {
  return request({
    url: '/sms/shipping-detail/antiwriteoff?id=' + id,
    method: 'get'
  })
}

// 获得配船明细
export function getShippingDetail(id) {
  return request({
    url: '/sms/shipping-detail/get?id=' + id,
    method: 'get'
  })
}

// 获得配船明细分页
export function getShippingDetailPage(params) {
  return request({
    url: '/sms/shipping-detail/page',
    method: 'get',
    params
  })
}

export function tradeSelectPage(params) {
  return request({
    url: '/sms/shipping-detail/tradeSelectPage',
    method: 'get',
    params
  })
}

// 导出配船明细 Excel
export function exportShippingDetailExcel(params) {
  return request({
    url: '/sms/shipping-detail/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（配船明细表明细项次档） ====================
export function selectItemByTrade(params) {
  return request({
    url: '/sms/shipping-detail/shipping-detail-items/selectItemByTrade',
    method: 'get',
    params
  })
}

export function batchCreateShippingItems(data) {
  return request({
    url: '/sms/shipping-detail/shipping-detail-items/batchCreateShippingItems',
    method: 'post',
    data
  })
}

// 获得配船明细表明细项次档分页
export function getShippingDetailItemsPage(params) {
  return request({
    url: '/sms/shipping-detail/shipping-detail-items/page',
    method: 'get',
    params
  })
}

// 新增配船明细表明细项次档
export function createShippingDetailItems(data) {
  return request({
    url: '/sms/shipping-detail/shipping-detail-items/create',
    method: 'post',
    data
  })
}

// 修改配船明细表明细项次档
export function updateShippingDetailItems(data) {
  return request({
    url: '/sms/shipping-detail/shipping-detail-items/update',
    method: 'post',
    data
  })
}

// 删除配船明细表明细项次档
export function deleteShippingDetailItems(id) {
  return request({
    url: '/sms/shipping-detail/shipping-detail-items/delete?id=' + id,
    method: 'delete'
  })
}

// 获得配船明细表明细项次档
export function getShippingDetailItems(id) {
  return request({
    url: '/sms/shipping-detail/shipping-detail-items/get?id=' + id,
    method: 'get'
  })
}

// 获得配船明细表明细项次档
export function groupbyItems(parentid, contractTradeId) {
  return request({
    url: '/sms/shipping-detail/shipping-detail-items/groupbyItems?parentid=' + parentid +'&contractTradeId=' + contractTradeId,
    method: 'get'
  })
}

// 导出配船明细 Excel
export function exportShippingDetailItemExcel(parentid) {
  return request({
    url: '/sms/shipping-detail/shipping-detail-items/export-excel?parentid='+parentid,
    method: 'get',
    responseType: 'blob'
  })
}
