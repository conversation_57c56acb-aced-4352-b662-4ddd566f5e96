import request from '@/utils/request'


// 创建产品页面设定
export function createGmPageSet(data) {
  return request({
    url: '/sms/gm-page-set/create',
    method: 'post',
    data: data
  })
}

// 更新产品页面设定
export function updateGmPageSet(data) {
  return request({
    url: '/sms/gm-page-set/update',
    method: 'put',
    data: data
  })
}

// 删除产品页面设定
export function deleteGmPageSet(id) {
  return request({
    url: '/sms/gm-page-set/delete?id=' + id,
    method: 'delete'
  })
}

// 获得产品页面设定
export function getGmPageSet(id) {
  return request({
    url: '/sms/gm-page-set/get?id=' + id,
    method: 'get'
  })
}

// 获得产品页面设定分页
export function getGmPageSetPage(params) {
  return request({
    url: '/sms/gm-page-set/page',
    method: 'get',
    params
  })
}
// 导出产品页面设定 Excel
export function exportGmPageSetExcel(params) {
  return request({
    url: '/sms/gm-page-set/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
