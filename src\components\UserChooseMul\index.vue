<template>
  <el-dialog class="user-choose-mul" title="选择人员" :visible.sync="visible" :close-on-click-modal="false"
             width="1400px" append-to-body>
    <div class="table-part">
      <!--      选择区域-->
      <div class="table-part-select">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="80px">
          <el-form-item label="用户名称" prop="username">
            <el-input
              v-model="queryParams.username"
              placeholder="请输入用户名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="用户昵称" prop="nickname">
            <el-input
              v-model="queryParams.nickname"
              placeholder="请输入用户昵称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table ref="table" v-loading="loading" row-key="userId" :data="dataList"
                  @selection-change="handleSelectionChange"
                  @row-click="handleSelectRow">
          <el-table-column type="selection" width="55" reserve-selection/>
          <el-table-column label="用户名称" align="center" prop="username" min-width="100" show-overflow-tooltip/>
          <el-table-column label="用户昵称" align="center" prop="nickname" min-width="150" show-overflow-tooltip/>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
      <!--      已选择区域-->
      <div class="table-part-selected">
        <SelectedArea :selectedData="selectedData" :deptOptions="deptOptionsControl" @closeSelect="closeSelect"/>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirm">确定</el-button>
      <el-button @click="visible=false">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {listUser} from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import SelectedArea from "@/components/UserChooseMul/components/SelectedArea.vue";
import {hiddenPartPhone} from "@/utils/common";

export default {
  name: "UserChooseMul",
  components: {Treeselect, SelectedArea},
  props: {
  },
  data() {
    return {
      visible: false,
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      dataList: [],
      selectedData: [],
      deptOptionsControl: [],
    }
  },
  computed: {
    params() {
      return {
        ...this.queryParams
      }
    }
  },
  methods: {
    hiddenPartPhone,
    show() {
      this.visible = true
      this.reset()
      this.resetQuery();

    },
    getList() {
      this.loading = true;
      listUser(this.params).then(response => {
        this.dataList = response.data.list
        this.total = response.data.total
        this.loading = false;
      });
    },
    reset() {
      this.deptOptionsControl = []
      this.selectedData = []
      this.$nextTick(() => {
        this.$refs.table.clearSelection()
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams = Object.assign({}, this.queryParams)
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedData = selection
    },
    //点击当前行，复选框选中。
    handleSelectRow(rowData) {
      this.$refs.table.toggleRowSelection(rowData);
    },
    //确认选择
    confirm() {
      if (this.selectedData.length <= 0) {
        return this.$modal.msgWarning('请选择至少选择一项')
      }
      this.$emit('selected', this.selectedData)
      this.visible = false
    },
    //删除内容
    closeSelect(row) {
      this.$refs.table.toggleRowSelection(row, false)
    }
  }
}
</script>

<style scoped lang="less">
.user-choose-mul {
  .table-part {
    display: flex;
    width: 100%;
    justify-content: space-between;

    .table-part-select {
      width: 60%;
      padding-right: 10px;
      border-right: 1px solid #EEEEEE;

      /deep/ .el-table .el-table__row {
        cursor: pointer;
      }
    }

    .table-part-selected {
      width: 40%;
      padding-left: 10px;
    }
  }
}
</style>
