import request from '@/utils/request'

// 创建资源池管理
export function createResourcePool(data) {
  return request({
    url: '/sms/resource-pool/create',
    method: 'post',
    data: data
  })
}

// 更新资源池管理
export function updateResourcePool(data) {
  return request({
    url: '/sms/resource-pool/update',
    method: 'put',
    data: data
  })
}

// 删除资源池管理
export function deleteResourcePool(id) {
  return request({
    url: '/sms/resource-pool/delete?id=' + id,
    method: 'delete'
  })
}

// 获得资源池管理
export function getResourcePool(id) {
  return request({
    url: '/sms/resource-pool/get?id=' + id,
    method: 'get'
  })
}

// 获得资源池管理分页
export function getResourcePoolPage(params) {
  return request({
    url: '/sms/resource-pool/page',
    method: 'get',
    params
  })
}
// 导出资源池管理 Excel
export function exportResourcePoolExcel(params) {
  return request({
    url: '/sms/resource-pool/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（资源池明细） ====================
    // 获得资源池明细分页
  export function getResourcePoolDetailPage(params) {
    return request({
      url: '/sms/resource-pool/resource-pool-detail/page',
      method: 'get',
      params
    })
  }
        // 新增资源池明细
  export function createResourcePoolDetail(data) {
    return request({
      url: '/sms/resource-pool/resource-pool-detail/create',
      method: 'post',
      data
    })
  }
  // 修改资源池明细
  export function updateResourcePoolDetail(data) {
    return request({
      url: '/sms/resource-pool/resource-pool-detail/update',
      method: 'put',
      data
    })
  }
  // 删除资源池明细
  export function deleteResourcePoolDetail(id) {
    return request({
      url: '/sms/resource-pool/resource-pool-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得资源池明细
  export function getResourcePoolDetail(id) {
    return request({
      url: '/sms/resource-pool/resource-pool-detail/get?id=' + id,
      method: 'get'
    })
  }

// 获得资源池明细
export function getResourcePoolNumDetail(params) {
  return request({
    url: '/sms/resource-pool/resource-pool-detail/getNumDetail',
    method: 'get',
    params
  })
}
