import request from '@/utils/request'

// 创建询单管理
export function createContractInquiry(data) {
  return request({
    url: '/sms/contract-inquiry/create',
    method: 'post',
    data: data
  })
}

// 更新询单管理
export function updateContractInquiry(data) {
  return request({
    url: '/sms/contract-inquiry/update',
    method: 'put',
    data: data
  })
}

// 删除询单管理
export function deleteContractInquiry(id) {
  return request({
    url: '/sms/contract-inquiry/delete?id=' + id,
    method: 'delete'
  })
}

// 获得询单管理
export function getContractInquiry(id) {
  return request({
    url: '/sms/contract-inquiry/get?id=' + id,
    method: 'get'
  })
}

// 获得询单管理
export function getContractInquirybyNo(id) {
  return request({
    url: '/sms/contract-inquiry/getbyNo?id=' + id,
    method: 'get'
  })
}


// 获得询单管理分页
export function getContractInquiryPage(params) {
  return request({
    url: '/sms/contract-inquiry/page',
    method: 'get',
    params
  })
}
// 导出询单管理 Excel
export function exportContractInquiryExcel(params) {
  return request({
    url: '/sms/contract-inquiry/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 生效流程
export function effectiveContractInquiry(id) {
  return request({
    url: '/sms/contract-inquiry/effect?id=' + id,
    method: 'get'
  })
}

// 取消生效
export function invalidContractInquiry(id) {
  return request({
    url: '/sms/contract-inquiry/invalid?id=' + id,
    method: 'get'
  })
}
