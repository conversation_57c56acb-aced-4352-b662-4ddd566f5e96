import request from '@/utils/request'

// 创建测试树
export function createCc(data) {
  return request({
    url: '/basic/cc/create',
    method: 'post',
    data: data
  })
}

// 更新测试树
export function updateCc(data) {
  return request({
    url: '/basic/cc/update',
    method: 'put',
    data: data
  })
}

// 删除测试树
export function deleteCc(id) {
  return request({
    url: '/basic/cc/delete?id=' + id,
    method: 'delete'
  })
}

// 获得测试树
export function getCc(id) {
  return request({
    url: '/basic/cc/get?id=' + id,
    method: 'get'
  })
}

// 获得测试树列表
export function getCcList(params) {
  return request({
    url: '/basic/cc/list',
    method: 'get',
    params
  })
}
// 导出测试树 Excel
export function exportCcExcel(params) {
  return request({
    url: '/basic/cc/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
