import request from '@/utils/request'

// 创建保单基本信息
export function createPolicyInfo(data) {
  return request({
    url: '/pms/policy-info/create',
    method: 'post',
    data: data
  })
}

// 更新保单基本信息
export function updatePolicyInfo(data) {
  return request({
    url: '/pms/policy-info/update',
    method: 'put',
    data: data
  })
}

// 删除保单基本信息
export function deletePolicyInfo(id) {
  return request({
    url: '/pms/policy-info/delete?id=' + id,
    method: 'delete'
  })
}

// 获得保单基本信息
export function getPolicyInfo(id) {
  return request({
    url: '/pms/policy-info/get?id=' + id,
    method: 'get'
  })
}

// 获得保单基本信息分页
export function getPolicyInfoPage(params) {
  return request({
    url: '/pms/policy-info/page',
    method: 'get',
    params
  })
}
// 导出保单基本信息 Excel
export function exportPolicyInfoExcel(params) {
  return request({
    url: '/pms/policy-info/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获得保单基本信息
export function getPolicyInfoByPo(pono) {
  return request({
    url: '/pms/policy-info/getPolicy?pono=' + pono,
    method: 'get'
  })
}
