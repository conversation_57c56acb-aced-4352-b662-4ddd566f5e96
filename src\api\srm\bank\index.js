import request from '@/utils/request'

// 创建银行信息
export function createBank(data) {
  return request({
    url: '/srm/bank/create',
    method: 'post',
    data: data
  })
}

// 更新银行信息
export function updateBank(data) {
  return request({
    url: '/srm/bank/update',
    method: 'put',
    data: data
  })
}

// 删除银行信息
export function deleteBank(id) {
  return request({
    url: '/srm/bank/delete?id=' + id,
    method: 'delete'
  })
}

// 获得银行信息
export function getBank(id) {
  return request({
    url: '/srm/bank/get?id=' + id,
    method: 'get'
  })
}

// 获得银行信息分页
export function getBankPage(params) {
  return request({
    url: '/srm/bank/page',
    method: 'get',
    params
  })
}
// 导出银行信息 Excel
export function exportBankExcel(params) {
  return request({
    url: '/srm/bank/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}