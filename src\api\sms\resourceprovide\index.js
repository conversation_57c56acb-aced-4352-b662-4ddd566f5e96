import request from '@/utils/request'

// 创建产品资源发放
export function createResourceProvide(data) {
  return request({
    url: '/sms/resource-provide/create',
    method: 'post',
    data: data
  })
}

// 更新产品资源发放
export function updateResourceProvide(data) {
  return request({
    url: '/sms/resource-provide/update',
    method: 'put',
    data: data
  })
}

// 删除产品资源发放
export function deleteResourceProvide(id) {
  return request({
    url: '/sms/resource-provide/delete?id=' + id,
    method: 'delete'
  })
}

// 获得产品资源发放
export function getResourceProvide(id) {
  return request({
    url: '/sms/resource-provide/get?id=' + id,
    method: 'get'
  })
}

// 获得产品资源发放分页
export function getResourceProvidePage(params) {
  return request({
    url: '/sms/resource-provide/page',
    method: 'get',
    params
  })
}
// 导出产品资源发放 Excel
export function exportResourceProvideExcel(params) {
  return request({
    url: '/sms/resource-provide/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 生效流程
export function effectiveResourceProvide(id) {
  return request({
    url: '/sms/resource-provide/effect?id=' + id,
    method: 'get'
  })
}

// 取消生效
export function invalidResourceProvide(id) {
  return request({
    url: '/sms/resource-provide/invalid?id=' + id,
    method: 'get'
  })
}

// ==================== 子表（产品资源发放明细） ====================
    // 获得产品资源发放明细分页
  export function getResourceProvideDetailPage(params) {
    return request({
      url: '/sms/resource-provide/resource-provide-detail/page',
      method: 'get',
      params
    })
  }

  export function getResourceProvideDetailPage2(params) {
    return request({
      url: '/sms/resource-provide/resource-provide-detail/page2',
      method: 'get',
      params
    })
  }
        // 新增产品资源发放明细
  export function createResourceProvideDetail(data) {
    return request({
      url: '/sms/resource-provide/resource-provide-detail/create',
      method: 'post',
      data
    })
  }
  // 修改产品资源发放明细
  export function updateResourceProvideDetail(data) {
    return request({
      url: '/sms/resource-provide/resource-provide-detail/update',
      method: 'post',
      data
    })
  }
  // 删除产品资源发放明细
  export function deleteResourceProvideDetail(id) {
    return request({
      url: '/sms/resource-provide/resource-provide-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得产品资源发放明细
  export function getResourceProvideDetail(id) {
    return request({
      url: '/sms/resource-provide/resource-provide-detail/get?id=' + id,
      method: 'get'
    })
  }

// 获得当前资源池明细的最后一笔发放资源
export function getLastWeekProvviceDetail(id) {
  return request({
    url: '/sms/resource-provide/resource-provide-detail/getLast?id=' + id,
    method: 'get'
  })
}
