import request from '@/utils/request'

// 创建需求计划
export function createMrp(data) {
  return request({
    url: '/pms/mrp/create',
    method: 'post',
    data: data
  })
}

// 更新需求计划
export function updateMrp(data) {
  return request({
    url: '/pms/mrp/update',
    method: 'put',
    data: data
  })
}

// 删除需求计划
export function deleteMrp(id) {
  return request({
    url: '/pms/mrp/delete?id=' + id,
    method: 'delete'
  })
}

// 获得需求计划
export function getMrp(id) {
  return request({
    url: '/pms/mrp/get?id=' + id,
    method: 'get'
  })
}

// 获得需求计划分页
export function getMrpPage(params) {
  return request({
    url: '/pms/mrp/page',
    method: 'get',
    params
  })
}
// 导出需求计划 Excel
export function exportMrpExcel(params) {
  return request({
    url: '/pms/mrp/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（需求计划明细） ====================
    // 获得需求计划明细分页
  export function getMrpDetailPage(params) {
    return request({
      url: '/pms/mrp/mrp-detail/page',
      method: 'get',
      params
    })
  }
  export function getMrpDetailBalancedPage(params) {
    return request({
      url: '/pms/mrp/mrp-detail/balanced/page',
      method: 'get',
      params
    })
  }
        // 新增需求计划明细
  export function createMrpDetail(data) {
    return request({
      url: '/pms/mrp/mrp-detail/create',
      method: 'post',
      data
    })
  }
  // 修改需求计划明细
  export function updateMrpDetail(data) {
    return request({
      url: '/pms/mrp/mrp-detail/update',
      method: 'post',
      data
    })
  }
  // 删除需求计划明细
  export function deleteMrpDetail(id) {
    return request({
      url: '/pms/mrp/mrp-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得需求计划明细
  export function getMrpDetail(id) {
    return request({
      url: '/pms/mrp/mrp-detail/get?id=' + id,
      method: 'get'
    })
  }
export function importTemplate() {
  return request({
    url: '/pms/mrp/mrp-detail/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}

// 获得计划单价
  export function getUnitprice(matrlno,year) {
    return request({
      url: `/pms/mrp/mrp-detail/getUnitprice?matrlno=${matrlno}&year=${year}`,
      method: 'get'
    })
  }

export function createMrpProcess(data) {
  return request({
    url: '/pms/mrp/process/create',
    method: 'post',
    data
  })
}
export function mrp2mpp(ids) {
  return request({
    url: '/pms/mrp/mrp-detail/balanced/'+ids,
    method: 'put',
  })
}
export function back2mpp(data) {
  return request({
    url: '/pms/mrp/mrp-detail/backup',
    method: 'put',
    data
  })
}

// //获取模板
// export function importTemplate() {
//   return request({
//     url: '/pms/mrp/get-import-template',
//     method: 'get',
//     responseType: 'blob'
//   })
// }
