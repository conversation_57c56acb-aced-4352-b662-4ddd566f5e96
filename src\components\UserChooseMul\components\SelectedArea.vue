<template>
  <div class="selected-area">
    <h2 style="margin-top: 0;">已选择人员</h2>
    <el-table ref="table" row-key="userId" :data="dataList" border>
      <el-table-column label="用户" align="center" prop="nickName" min-width="150">
        <template #default="{row}">
          <el-tag
            class="user-tag-item"
            :key="row.userId"
            closable @close="closeTag(row)">
            {{ row.nickName }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {groupByField} from "@/utils/common";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "SelectedArea",
  components: {Treeselect},
  props: {
    //已选择的内容
    selectedData: {
      type: Array,
      default: () => []
    },
    deptOptions: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    dataList() {
      console.log(this.selectedData)
      return this.selectedData
    }
  },
  methods: {
    //关闭tag
    closeTag(item) {
      this.$emit('closeSelect', item)
    }
  }
}
</script>

<style scoped lang="less">
  .selected-area {
    .user-tag-item {
      margin: 2px;
    }
  }
</style>
