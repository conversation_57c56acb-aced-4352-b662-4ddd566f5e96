import request from '@/utils/request'

// 创建SRM_SUPPLIER供应商
export function createSupplier(data) {
  return request({
    url: '/srm/supplier/create',
    method: 'post',
    data: data
  })
}

// 更新SRM_SUPPLIER供应商
export function updateSupplier(data) {
  return request({
    url: '/srm/supplier/update',
    method: 'put',
    data: data
  })
}

// 删除SRM_SUPPLIER供应商
export function deleteSupplier(id) {
  return request({
    url: '/srm/supplier/delete?id=' + id,
    method: 'delete'
  })
}

// 获得SRM_SUPPLIER供应商
export function getSupplier(id) {
  return request({
    url: '/srm/supplier/get?id=' + id,
    method: 'get'
  })
}

// 获得SRM_SUPPLIER供应商分页
export function getSupplierPage(params) {
  return request({
    url: '/srm/supplier/page',
    method: 'get',
    params
  })
}
// 导出SRM_SUPPLIER供应商 Excel
export function exportSupplierExcel(params) {
  return request({
    url: '/srm/supplier/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}