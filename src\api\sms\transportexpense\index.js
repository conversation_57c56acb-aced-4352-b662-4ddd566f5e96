import request from '@/utils/request'

// 创建运杂费报支管理
export function createTransportExpense(data) {
  return request({
    url: '/sms/transport-expense/create',
    method: 'post',
    data: data
  })
}

// 更新运杂费报支管理
export function updateTransportExpense(data) {
  return request({
    url: '/sms/transport-expense/update',
    method: 'put',
    data: data
  })
}

// 删除运杂费报支管理
export function deleteTransportExpense(id) {
  return request({
    url: '/sms/transport-expense/delete?id=' + id,
    method: 'delete'
  })
}

export function pushNCTransportExpense(id) {
  return request({
    url: '/sms/transport-expense/pushNCTransportExpense?id=' + id,
    method: 'get'
  })
}

// 获得运杂费报支管理
export function getTransportExpense(id) {
  return request({
    url: '/sms/transport-expense/get?id=' + id,
    method: 'get'
  })
}

// 获得运杂费报支管理分页
export function getTransportExpensePage(params) {
  return request({
    url: '/sms/transport-expense/page',
    method: 'get',
    params
  })
}

// 导出运杂费报支管理 Excel
export function exportTransportExpenseExcel(params) {
  return request({
    url: '/sms/transport-expense/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（发票明细） ====================
// 获得发票明细分页
export function getTransportExpenseInvoiceDetailPage(params) {
  return request({
    url: '/sms/transport-expense/transport-expense-invoice-detail/page',
    method: 'get',
    params
  })
}

// 新增发票明细
export function createTransportExpenseInvoiceDetail(data) {
  return request({
    url: '/sms/transport-expense/transport-expense-invoice-detail/create',
    method: 'post',
    data
  })
}

// 修改发票明细
export function updateTransportExpenseInvoiceDetail(data) {
  return request({
    url: '/sms/transport-expense/transport-expense-invoice-detail/update',
    method: 'post',
    data
  })
}

// 删除发票明细
export function deleteTransportExpenseInvoiceDetail(id) {
  return request({
    url: '/sms/transport-expense/transport-expense-invoice-detail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得发票明细
export function getTransportExpenseInvoiceDetail(id) {
  return request({
    url: '/sms/transport-expense/transport-expense-invoice-detail/get?id=' + id,
    method: 'get'
  })
}

// 批量创建发票明细
export function batchCreateInvoice(data) {
  return request({
    url: '/sms/transport-expense/transport-expense-invoice-detail/batchCreateInvoice',
    method: 'post',
    data
  })
}

// ==================== 子表（冲抵暂估明细） ====================
// 获得冲抵暂估明细分页
export function getTransportExpenseOffsetDetailPage(params) {
  return request({
    url: '/sms/transport-expense/transport-expense-offset-detail/page',
    method: 'get',
    params
  })
}

// 新增冲抵暂估明细
export function createTransportExpenseOffsetDetail(data) {
  return request({
    url: '/sms/transport-expense/transport-expense-offset-detail/create',
    method: 'post',
    data
  })
}

// 修改冲抵暂估明细
export function updateTransportExpenseOffsetDetail(data) {
  return request({
    url: '/sms/transport-expense/transport-expense-offset-detail/update',
    method: 'post',
    data
  })
}

// 删除冲抵暂估明细
export function deleteTransportExpenseOffsetDetail(id) {
  return request({
    url: '/sms/transport-expense/transport-expense-offset-detail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得冲抵暂估明细
export function getTransportExpenseOffsetDetail(id) {
  return request({
    url: '/sms/transport-expense/transport-expense-offset-detail/get?id=' + id,
    method: 'get'
  })
}

// 批量操作
export function batchOperateOffsetDetail(data) {
  return request({
    url: '/sms/transport-expense/transport-expense-offset-detail/batchOperateOffsetDetail',
    method: 'post',
    data
  })
}
