import request from '@/utils/request'

// 创建船运计划
export function createShipPlan(data) {
  return request({
    url: '/sms/ship-plan/create',
    method: 'post',
    data: data
  })
}

// 更新船运计划
export function updateShipPlan(data) {
  return request({
    url: '/sms/ship-plan/update',
    method: 'put',
    data: data
  })
}

// 删除船运计划
export function deleteShipPlan(id) {
  return request({
    url: '/sms/ship-plan/delete?id=' + id,
    method: 'delete'
  })
}

// 获得船运计划
export function getShipPlan(id) {
  return request({
    url: '/sms/ship-plan/get?id=' + id,
    method: 'get'
  })
}

// 获得船运计划分页
export function getShipPlanPage(params) {
  return request({
    url: '/sms/ship-plan/page',
    method: 'get',
    params
  })
}
// 导出船运计划 Excel
export function exportShipPlanExcel(params) {
  return request({
    url: '/sms/ship-plan/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 生效流程
export function effectiveShipResource(id) {
  return request({
    url: '/sms/ship-plan/effect?id=' + id,
    method: 'get'
  })
}

// ==================== 子表（船运计划明细） ====================
    // 获得船运计划明细分页
  export function getShipPlanDetailPage(params) {
    return request({
      url: '/sms/ship-plan/ship-plan-detail/page',
      method: 'get',
      params
    })
  }

export function getAllShipPlanDetailPage(params) {
  return request({
    url: '/sms/ship-plan/ship-plan-detail/allpage',
    method: 'get',
    params
  })
}
        // 新增船运计划明细
  export function createShipPlanDetail(data) {
    return request({
      url: '/sms/ship-plan/ship-plan-detail/create',
      method: 'post',
      data
    })
  }
  // 修改船运计划明细
  export function updateShipPlanDetail(data) {
    return request({
      url: '/sms/ship-plan/ship-plan-detail/update',
      method: 'put',
      data
    })
  }
  // 删除船运计划明细
  export function deleteShipPlanDetail(id) {
    return request({
      url: '/sms/ship-plan/ship-plan-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得船运计划明细
  export function getShipPlanDetail(id) {
    return request({
      url: '/sms/ship-plan/ship-plan-detail/get?id=' + id,
      method: 'get'
    })
  }
