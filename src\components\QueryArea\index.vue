<template>
  <div class="query-area">
    <div class="query-area-show">
      <div class="query-area-show-slot">
        <slot name="show"></slot>
      </div>
      <div class="query-area-show-control">
        <el-button icon="el-icon-d-arrow-left" circle size="small"
                   :style="{transform: `rotate(${hiddenShow?'90deg':'-90deg'})`}"
                   @click="hiddenShow=!hiddenShow"></el-button>
        <span v-if="['number','string'].includes(typeof total)" class="query-area-show-control__total">
          共 {{ total }} 条
        </span>
      </div>
    </div>
    <div class="query-area-hidden" v-show="hiddenShow">
      <slot name="hidden"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "QueryArea",
  props: {
    total: {
      type: [Number, String],
      default: undefined
    }
  },
  data() {
    return {
      hiddenShow: false,//控制更多搜索条件的显示与隐藏
    }
  }
}
</script>

<style scoped lang="scss">
.query-area {
  .query-area-show {
    display: flex;

    .query-area-show-control__total {
      font-size: 14px;
      color: inherit;
      margin-left: 10px;
      white-space: nowrap;
    }
  }

  .query-area-hidden {

  }
}
</style>
