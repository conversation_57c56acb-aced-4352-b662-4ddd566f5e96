import request from '@/utils/request'

// 创建应收账款主档
export function createRcv(data) {
  return request({
    url: '/sms/rcv/create',
    method: 'post',
    data: data
  })
}

// 更新应收账款主档
export function updateRcv(data) {
  return request({
    url: '/sms/rcv/update',
    method: 'put',
    data: data
  })
}

// 删除应收账款主档
export function deleteRcv(id) {
  return request({
    url: '/sms/rcv/delete?id=' + id,
    method: 'delete'
  })
}

// 获得应收账款主档
export function getRcv(id) {
  return request({
    url: '/sms/rcv/get?id=' + id,
    method: 'get'
  })
}

// 获得应收账款主档分页
export function getRcvPage(params) {
  return request({
    url: '/sms/rcv/page',
    method: 'get',
    params
  })
}
// 导出应收账款主档 Excel
export function exportRcvExcel(params) {
  return request({
    url: '/sms/rcv/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（已收货款明细档） ====================
    // 获得已收货款明细档分页
  export function getInRcvDetailPage(params) {
    return request({
      url: '/sms/rcv/in-rcv-detail/page',
      method: 'get',
      params
    })
  }
        // 新增已收货款明细档
  export function createInRcvDetail(data) {
    return request({
      url: '/sms/rcv/in-rcv-detail/create',
      method: 'post',
      data
    })
  }
  // 修改已收货款明细档
  export function updateInRcvDetail(data) {
    return request({
      url: '/sms/rcv/in-rcv-detail/update',
      method: 'post',
      data
    })
  }
  // 删除已收货款明细档
  export function deleteInRcvDetail(id) {
    return request({
      url: '/sms/rcv/in-rcv-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得已收货款明细档
  export function getInRcvDetail(id) {
    return request({
      url: '/sms/rcv/in-rcv-detail/get?id=' + id,
      method: 'get'
    })
  }

// ==================== 子表（已锁货款明细档） ====================
    // 获得已锁货款明细档分页
  export function getLockRcvDetailPage(params) {
    return request({
      url: '/sms/rcv/lock-rcv-detail/page',
      method: 'get',
      params
    })
  }
        // 新增已锁货款明细档
  export function createLockRcvDetail(data) {
    return request({
      url: '/sms/rcv/lock-rcv-detail/create',
      method: 'post',
      data
    })
  }
  // 修改已锁货款明细档
  export function updateLockRcvDetail(data) {
    return request({
      url: '/sms/rcv/lock-rcv-detail/update',
      method: 'post',
      data
    })
  }
  // 删除已锁货款明细档
  export function deleteLockRcvDetail(id) {
    return request({
      url: '/sms/rcv/lock-rcv-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得已锁货款明细档
  export function getLockRcvDetail(id) {
    return request({
      url: '/sms/rcv/lock-rcv-detail/get?id=' + id,
      method: 'get'
    })
  }

// ==================== 子表（应收账款明细档） ====================
    // 获得应收账款明细档分页
  export function getRcvDetailPage(params) {
    return request({
      url: '/sms/rcv/rcv-detail/page',
      method: 'get',
      params
    })
  }
        // 新增应收账款明细档
  export function createRcvDetail(data) {
    return request({
      url: '/sms/rcv/rcv-detail/create',
      method: 'post',
      data
    })
  }
  // 修改应收账款明细档
  export function updateRcvDetail(data) {
    return request({
      url: '/sms/rcv/rcv-detail/update',
      method: 'post',
      data
    })
  }
  // 删除应收账款明细档
  export function deleteRcvDetail(id) {
    return request({
      url: '/sms/rcv/rcv-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得应收账款明细档
  export function getRcvDetail(id) {
    return request({
      url: '/sms/rcv/rcv-detail/get?id=' + id,
      method: 'get'
    })
  }
// ==================== 子表（已收授信明细档） ====================
// 获得已收授信明细档分页
export function getInCreditDetailPage(params) {
  return request({
    url: '/sms/rcv/in-credit-detail/page',
    method: 'get',
    params
  })
}
// 新增已收授信明细档
export function createInCreditDetail(data) {
  return request({
    url: '/sms/rcv/in-credit-detail/create',
    method: 'post',
    data
  })
}
// 修改已收授信明细档
export function updateInCreditDetail(data) {
  return request({
    url: '/sms/rcv/in-credit-detail/update',
    method: 'post',
    data
  })
}
// 删除已收授信明细档
export function deleteInCreditDetail(id) {
  return request({
    url: '/sms/rcv/in-credit-detail/delete?id=' + id,
    method: 'delete'
  })
}
// 获得已收授信明细档
export function getInCreditDetail(id) {
  return request({
    url: '/sms/rcv/in-credit-detail/get?id=' + id,
    method: 'get'
  })
}
