import request from '@/utils/request'

// 创建测试树
export function createCc(data) {
  return request({
    url: '/ceshi/cc/create',
    method: 'post',
    data: data
  })
}

// 更新测试树
export function updateCc(data) {
  return request({
    url: '/ceshi/cc/update',
    method: 'put',
    data: data
  })
}

// 删除测试树
export function deleteCc(id) {
  return request({
    url: '/ceshi/cc/delete?id=' + id,
    method: 'delete'
  })
}

// 获得测试树
export function getCc(id) {
  return request({
    url: '/ceshi/cc/get?id=' + id,
    method: 'get'
  })
}

// 获得测试树分页
export function getCcPage(params) {
  return request({
    url: '/ceshi/cc/page',
    method: 'get',
    params
  })
}
// 导出测试树 Excel
export function exportCcExcel(params) {
  return request({
    url: '/ceshi/cc/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
