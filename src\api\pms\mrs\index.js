import request from '@/utils/request'

// 创建验收入储
export function createMaterialReceiptStorage(data) {
  return request({
    url: '/pms/material-receipt-storage/create',
    method: 'post',
    data: data
  })
}

// 更新验收入储
export function updateMaterialReceiptStorage(data) {
  return request({
    url: '/pms/material-receipt-storage/update',
    method: 'put',
    data: data
  })
}

// 删除验收入储
export function deleteMaterialReceiptStorage(id) {
  return request({
    url: '/pms/material-receipt-storage/delete?id=' + id,
    method: 'delete'
  })
}

// 获得验收入储
export function getMaterialReceiptStorage(id) {
  return request({
    url: '/pms/material-receipt-storage/get?id=' + id,
    method: 'get'
  })
}

// 获得验收入储分页
export function getMaterialReceiptStoragePage(params) {
  return request({
    url: '/pms/material-receipt-storage/page',
    method: 'get',
    params
  })
}
// 导出验收入储 Excel
export function exportMaterialReceiptStorageExcel(params) {
  return request({
    url: '/pms/material-receipt-storage/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
export function importTemplate() {
  return request({
    url: '/pms/material-receipt-storage/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}

export function batching(ids,chkno) {
  return request({
    url: '/pms/material-receipt-storage/batching?ids=' + ids+"&chkno="+chkno,
    method: 'put'
  })
}
export function unBatching(ids) {
  return request({
    url: '/pms/material-receipt-storage/unBatching?ids=' + ids,
    method: 'put'
  })
}
export function confirm(ids) {
  return request({
    url: '/pms/material-receipt-storage/confirm?ids=' + ids,
    method: 'put'
  })
}
export function cancel(ids) {
  return request({
    url: '/pms/material-receipt-storage/cancel?ids=' + ids,
    method: 'put'
  })
}
