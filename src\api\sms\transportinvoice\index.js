import request from '@/utils/request'

// 创建运杂费发票管理
export function createTransportInvoice(data) {
  return request({
    url: '/sms/transport-invoice/create',
    method: 'post',
    data: data
  })
}

// 更新运杂费发票管理
export function updateTransportInvoice(data) {
  return request({
    url: '/sms/transport-invoice/update',
    method: 'put',
    data: data
  })
}

// 删除运杂费发票管理
export function deleteTransportInvoice(id) {
  return request({
    url: '/sms/transport-invoice/delete?id=' + id,
    method: 'delete'
  })
}

// 获得运杂费发票管理
export function getTransportInvoice(id) {
  return request({
    url: '/sms/transport-invoice/get?id=' + id,
    method: 'get'
  })
}

export function auditTransportInvoice(id) {
  return request({
    url: '/sms/transport-invoice/auditTransportInvoice?id=' + id,
    method: 'get'
  })
}

// 获得运杂费发票管理分页
export function getTransportInvoicePage(params) {
  return request({
    url: '/sms/transport-invoice/page',
    method: 'get',
    params
  })
}
// 导出运杂费发票管理 Excel
export function exportTransportInvoiceExcel(params) {
  return request({
    url: '/sms/transport-invoice/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
