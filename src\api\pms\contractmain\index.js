import request from '@/utils/request'

// 创建法务合同基本信息
export function createContractMain(data) {
  return request({
    url: '/pms/contract-main/create',
    method: 'post',
    data: data
  })
}

// 更新法务合同基本信息
export function updateContractMain(data) {
  return request({
    url: '/pms/contract-main/update',
    method: 'put',
    data: data
  })
}

// 删除法务合同基本信息
export function deleteContractMain(id) {
  return request({
    url: '/pms/contract-main/delete?id=' + id,
    method: 'delete'
  })
}

// 获得法务合同基本信息
export function getContractMain(id) {
  return request({
    url: '/pms/contract-main/get?id=' + id,
    method: 'get'
  })
}
export function getContractMainByPoNo(poNo,poVer) {
  return request({
    url: '/pms/contract-main/get?poNo=' + poNo+"&poVer="+poVer,
    method: 'get'
  })
}
// 获得法务合同基本信息分页
export function getContractMainPage(params) {
  return request({
    url: '/pms/contract-main/page',
    method: 'get',
    params
  })
}
// 导出法务合同基本信息 Excel
export function exportContractMainExcel(params) {
  return request({
    url: '/pms/contract-main/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
export function selectAut(userId) {
  return request({
    url: '/pms/contract-main/getAut?userId=' + userId,
    method: 'get'
  })
}
export function selectSeal(userId) {
  return request({
    url: '/pms/contract-main/getSeal?userId=' + userId,
    method: 'get'
  })
}
export function sendFw(data) {
  return request({
    url: '/pms/contract-main/sendFw',
    method: 'post',
    data: data
  })
}
export function sendPerform(data) {
  return request({
    url: '/pms/contract-main/sendPerform',
    method: 'post',
    data: data
  })
}
// ==================== 子表（合同签章子） ====================
    // 获得合同签章子分页
  export function getContractSealPage(params) {
    return request({
      url: '/pms/contract-main/contract-seal/page',
      method: 'get',
      params
    })
  }
        // 新增合同签章子
  export function createContractSeal(data) {
    return request({
      url: '/pms/contract-main/contract-seal/create',
      method: 'post',
      data
    })
  }
  // 修改合同签章子
  export function updateContractSeal(data) {
    return request({
      url: '/pms/contract-main/contract-seal/update',
      method: 'put',
      data
    })
  }
  // 删除合同签章子
  export function deleteContractSeal(id) {
    return request({
      url: '/pms/contract-main/contract-seal/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得合同签章子
  export function getContractSeal(id) {
    return request({
      url: '/pms/contract-main/contract-seal/get?id=' + id,
      method: 'get'
    })
  }
