import request from '@/utils/request'

// 创建反馈意见
export function createFeedback(data) {
  return request({
    url: '/cust/feedback/create',
    method: 'post',
    data: data
  })
}

// 更新反馈意见
export function updateFeedback(data) {
  return request({
    url: '/cust/feedback/update',
    method: 'put',
    data: data
  })
}

// 删除反馈意见
export function deleteFeedback(id) {
  return request({
    url: '/cust/feedback/delete?id=' + id,
    method: 'delete'
  })
}

// 获得反馈意见
export function getFeedback(id) {
  return request({
    url: '/cust/feedback/get?id=' + id,
    method: 'get'
  })
}

// 获得反馈意见分页
export function getFeedbackPage(params) {
  return request({
    url: '/cust/feedback/page',
    method: 'get',
    params
  })
}
// 导出反馈意见 Excel
export function exportFeedbackExcel(params) {
  return request({
    url: '/cust/feedback/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
