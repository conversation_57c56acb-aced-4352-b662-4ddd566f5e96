import request from '@/utils/request'

// 创建入库
export function createWmsTrade(data) {
  return request({
    url: '/pms/wms-trade/create',
    method: 'post',
    data: data
  })
}

// 更新入库
export function updateWmsTrade(data) {
  return request({
    url: '/pms/wms-trade/update',
    method: 'put',
    data: data
  })
}

// 删除入库
export function deleteWmsTrade(id) {
  return request({
    url: '/pms/wms-trade/delete?id=' + id,
    method: 'delete'
  })
}

// 获得入库
export function getWmsTrade(id) {
  return request({
    url: '/pms/wms-trade/get?id=' + id,
    method: 'get'
  })
}

// 获得入库分页
export function getWmsTradePage(params) {
  return request({
    url: '/pms/wms-trade/page',
    method: 'get',
    params
  })
}
// 导出入库 Excel
export function exportWmsTradeExcel(params) {
  return request({
    url: '/pms/wms-trade/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 下载模板
export function importTemplate() {
  return request({
    url: '/pms/wms-trade/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}

// 批量新增
export function batchInsertWmsTrade(reqList) {
  return request({
    url: '/pms/wms-trade/batch',
    method: 'post',
    data: reqList
  })
}

// 获得申请确认
export function confirmWmsTrade(id) {
  return request({
    url: '/pms/wms-trade/confirm?id=' + id,
    method: 'get'
  })
}

// 取消申请确认
export function cancelWmsTrade(id) {
  return request({
    url: '/pms/wms-trade/cancelConfirm?id=' + id,
    method: 'get'
  })
}

//---------------------------销售出库
export function createWmsTradeK(data) {
  return request({
    url: '/pms/wms-trade/createK',
    method: 'post',
    data: data
  })
}

// 更新入库
export function updateWmsTradeK(data) {
  return request({
    url: '/pms/wms-trade/updateK',
    method: 'put',
    data: data
  })
}
export function batchInsertWmsTradeK(reqList) {
  return request({
    url: '/pms/wms-trade/batchK',
    method: 'post',
    data: reqList
  })
}
// 获得申请确认
export function confirmWmsTradeK(id) {
  let data ={
    "id":id
  }
  return request({
    url: '/pms/wms-trade/confirmK' ,
    method: 'post',
    data: data
  })
}

// 取消申请确认
export function cancelWmsTradeK(id) {
  let data ={
    "id":id
  }
  return request({
    url: '/pms/wms-trade/cancelConfirmK',
    method: 'post',
    data: data
  })
}
export function importKTemplate() {
  return request({
    url: '/pms/wms-trade/get-import-templateK',
    method: 'get',
    responseType: 'blob'
  })
}

// 获得申请确认
export function checkQty(data) {
  return request({
    url: '/pms/wms-trade/checkQty' ,
    method: 'post',
    data: data
  })
}
// ==================== 子表（交易申请明细） ====================
// 获得交易申请明细
export function getTradeDetailByParentId(parentId) {
  return request({
    url: '/pms/wms-trade/trade-detail/get-by-parent-id?parentId=' + parentId,
    method: 'get'
  })
}
