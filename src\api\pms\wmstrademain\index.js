import request from '@/utils/request'

// 创建物料交易申请主档
export function createWmsTradeMain(data) {
  return request({
    url: '/pms/wms-trade-main/create',
    method: 'post',
    data: data
  })
}

// 更新物料交易申请主档
export function updateWmsTradeMain(data) {
  return request({
    url: '/pms/wms-trade-main/update',
    method: 'put',
    data: data
  })
}

// 删除物料交易申请主档
export function deleteWmsTradeMain(id) {
  return request({
    url: '/pms/wms-trade-main/delete?id=' + id,
    method: 'delete'
  })
}

// 获得物料交易申请主档
export function getWmsTradeMain(id) {
  return request({
    url: '/pms/wms-trade-main/get?id=' + id,
    method: 'get'
  })
}

// 获得物料交易申请主档分页
export function getWmsTradeMainPage(params) {
  return request({
    url: '/pms/wms-trade-main/page',
    method: 'get',
    params
  })
}
// 导出物料交易申请主档 Excel
export function exportWmsTradeMainExcel(params) {
  return request({
    url: '/pms/wms-trade-main/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（物料交易明细） ====================
    // 获得物料交易明细分页
  export function getWmsTradePage(params) {
    return request({
      url: '/pms/wms-trade-main/wms-trade/page',
      method: 'get',
      params
    })
  }
        // 新增物料交易明细
  export function createWmsTrade(data) {
    return request({
      url: '/pms/wms-trade-main/wms-trade/create',
      method: 'post',
      data
    })
  }
  // 修改物料交易明细
  export function updateWmsTrade(data) {
    return request({
      url: '/pms/wms-trade-main/wms-trade/update',
      method: 'post',
      data
    })
  }
  // 删除物料交易明细
  export function deleteWmsTrade(id) {
    return request({
      url: '/pms/wms-trade-main/wms-trade/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得物料交易明细
  export function getWmsTrade(id) {
    return request({
      url: '/pms/wms-trade-main/wms-trade/get?id=' + id,
      method: 'get'
    })
  }

// 获得申请确认
export function confirmWmsTradeMain(id) {
  return request({
    url: '/pms/wms-trade-main/confirm?id=' + id,
    method: 'get'
  })
}

// 获得申请取消
export function cancelWmsTradeMain(id) {
  return request({
    url: '/pms/wms-trade-main/cancelConfirm?id=' + id,
    method: 'get'
  })
}

// 主档审核
export function auditWmsTradeMain(id) {
  return request({
    url: '/pms/wms-trade-main/audit?id=' + id,
    method: 'get'
  })
}

// 获得申请确认
export function confirmWmsTrade(id) {
  return request({
    url: '/pms/wms-trade-main/wms-trade/confirmDetail?id=' + id,
    method: 'get'
  })
}

// 获得申请取消
export function cancelWmsTrade(id) {
  return request({
    url: '/pms/wms-trade-main/wms-trade/cancelDetail?id=' + id,
    method: 'get'
  })
}

// 明细审核
export function auditWmsTradeDetail(id) {
  return request({
    url: '/pms/wms-trade-main/wms-trade/audit?id=' + id,
    method: 'get'
  })
}

// 创建物料交易中间
export function createWmsTradeDetail0(data) {
  return request({
    url: '/pms/wms-trade-main/wms-trade-detail0/create',
    method: 'post',
    data: data
  })
}

// 更新物料交易中间
export function updateWmsTradeDetail0(data) {
  return request({
    url: '/pms/wms-trade-main/wms-trade-detail0/update',
    method: 'put',
    data: data
  })
}

// 删除物料交易中间
export function deleteWmsTradeDetail0(id) {
  return request({
    url: '/pms/wms-trade-main/wms-trade-detail0/delete?id=' + id,
    method: 'delete'
  })
}

// 获得物料交易中间
export function getWmsTradeDetail0(id) {
  return request({
    url: '/pms/wms-trade-main/wms-trade-detail0/get?id=' + id,
    method: 'get'
  })
}

// 获得物料交易中间分页
export function getWmsTradeDetail0Page(params) {
  return request({
    url: '/pms/wms-trade-main/wms-trade-detail0/page',
    method: 'get',
    params
  })
}

// 获得物料交易中间分页
export function batchInsertWmsTradeDetail0(data) {
  return request({
    url: '/pms/wms-trade-main/wms-trade-detail0/batch',
    method: 'post',
    data: data
  })
}

// 下载模板
export function importTemplate() {
  return request({
    url: '/pms/wms-trade-main/wms-trade/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}

// 拆分交易明细
export function batchSplitCaseWmsTrade(data) {
  return request({
    url: '/pms/wms-trade-main/wms-trade/batchSplit',
    method: 'post',
    data: data
  })
}

// 获得物料交易中间分页
export function batchInsertWmsTrade(data) {
  return request({
    url: '/pms/wms-trade-main/wms-trade/batch',
    method: 'post',
    data: data
  })
}

// 获得物料交易中间分页
export function batchInsertWmsTrade2(data) {
  return request({
    url: '/pms/wms-trade-main/wms-trade/batch2',
    method: 'post',
    data: data
  })
}

// 生成盘盈亏单
export function produceWmsTrade(id) {
  return request({
    url: '/pms/wms-trade-main/wms-trade/produce?id=' + id,
    method: 'get'
  })
}

