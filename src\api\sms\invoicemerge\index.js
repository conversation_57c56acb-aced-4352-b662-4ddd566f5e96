import request from '@/utils/request'

// 发票合并
export function createInvoiceMerge(data) {
  return request({
    url: '/sms/invoice-merge/merge',
    method: 'post',
    data: data
  })
}

//取消发票合并
export function cancleInvoiceMerge(data) {
  return request({
    url: '/sms/invoice-merge/cancle',
    method: 'post',
    data: data
  })
}

// 录入发票的发票信息
export function updateInvoiceMerge(data) {
  return request({
    url: '/sms/invoice-merge/update',
    method: 'put',
    data: data
  })
}

// 获得发票合并管理
export function getInvoiceMerge(revenueNo) {
  return request({
    url: '/sms/invoice-merge/get?revenueNo=' + revenueNo,
    method: 'get'
  })
}

// 获得发票合并管理分页
export function getInvoiceMergePage(params) {
  return request({
    url: '/sms/invoice-merge/page',
    method: 'get',
    params
  })
}

// 获得发票合并明细档
export function getMergeInvoiceDetailPage(params) {
  return request({
    url: '/sms/invoice-merge/merge-invoice-detail-page',
    method: 'get',
    params
  })
}

// 导出发票合并管理 Excel
export function exportInvoiceMergeExcel(params) {
  return request({
    url: '/sms/invoice-merge/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
