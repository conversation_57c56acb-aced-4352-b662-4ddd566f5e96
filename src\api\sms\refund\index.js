import request from '@/utils/request'

// 创建退款单主档
export function createRefund(data) {
  return request({
    url: '/sms/refund/create',
    method: 'post',
    data: data
  })
}

// 更新退款单主档
export function updateRefund(data) {
  return request({
    url: '/sms/refund/update',
    method: 'put',
    data: data
  })
}

// 删除退款单主档
export function deleteRefund(id) {
  return request({
    url: '/sms/refund/delete?id=' + id,
    method: 'delete'
  })
}

// 获得退款单主档
export function getRefund(id) {
  return request({
    url: '/sms/refund/get?id=' + id,
    method: 'get'
  })
}

// 获得退款单主档分页
export function getRefundPage(params) {
  return request({
    url: '/sms/refund/page',
    method: 'get',
    params
  })
}
// 导出退款单主档 Excel
export function exportRefundExcel(params) {
  return request({
    url: '/sms/refund/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
export function confirmRefund(id) {
  return request({
    url: '/sms/refund/confirm?id=' + id,
    method: 'get'
  })
}
export function cancelConfirmRefund(id) {
  return request({
    url: '/sms/refund/cancelConfirm?id=' + id,
    method: 'get'
  })
}
// ==================== 子表（退款单明细档） ====================
    // 获得退款单明细档分页
  export function getRefundDetailPage(params) {
    return request({
      url: '/sms/refund/refund-detail/page',
      method: 'get',
      params
    })
  }
        // 新增退款单明细档
  export function createRefundDetail(data) {
    return request({
      url: '/sms/refund/refund-detail/create',
      method: 'post',
      data
    })
  }
  // 修改退款单明细档
  export function updateRefundDetail(data) {
    return request({
      url: '/sms/refund/refund-detail/update',
      method: 'put',
      data
    })
  }
  // 删除退款单明细档
  export function deleteRefundDetail(id) {
    return request({
      url: '/sms/refund/refund-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得退款单明细档
  export function getRefundDetail(id) {
    return request({
      url: '/sms/refund/refund-detail/get?id=' + id,
      method: 'get'
    })
  }
