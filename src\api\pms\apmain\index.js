import request from '@/utils/request'

// 创建报支记录主档
export function createApMain(data) {
  return request({
    url: '/pms/ap-main/create',
    method: 'post',
    data: data
  })
}

// 更新报支记录主档
export function updateApMain(data) {
  return request({
    url: '/pms/ap-main/update',
    method: 'put',
    data: data
  })
}

// 删除报支记录主档
export function deleteApMain(id) {
  return request({
    url: '/pms/ap-main/delete?id=' + id,
    method: 'delete'
  })
}

// 获得报支记录主档
export function getApMain(id) {
  return request({
    url: '/pms/ap-main/get?id=' + id,
    method: 'get'
  })
}

// 获得报支记录主档分页
export function getApMainPage(params) {
  return request({
    url: '/pms/ap-main/page',
    method: 'get',
    params
  })
}

// 导出报支记录主档 Excel
export function exportApMainExcel(params) {
  return request({
    url: '/pms/ap-main/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function confirmApMain(id) {
  return request({
    url: '/pms/ap-main/confirm?id=' + id,
    method: 'get'
  })
}
export function cancelConfirmApMain(id) {
  return request({
    url: '/pms/ap-main/cancelConfirm?id=' + id,
    method: 'get'
  })
}
export function confirmApMainYF(id) {
  return request({
    url: '/pms/ap-main/confirmYF?id=' + id,
    method: 'get'
  })
}
export function cancelConfirmApMainYF(id) {
  return request({
    url: '/pms/ap-main/cancelConfirmYF?id=' + id,
    method: 'get'
  })
}
// ==================== 子表（报支记录明细档） ====================
// 获得报支记录明细档分页
export function getApDetailPage(params) {
  return request({
    url: '/pms/ap-main/ap-detail/page',
    method: 'get',
    params
  })
}

// 新增报支记录明细档
export function createApDetail(data) {
  return request({
    url: '/pms/ap-main/ap-detail/create',
    method: 'post',
    data
  })
}

// 修改报支记录明细档
export function updateApDetail(data) {
  return request({
    url: '/pms/ap-main/ap-detail/update',
    method: 'post',
    data
  })
}

// 删除报支记录明细档
export function deleteApDetail(id) {
  return request({
    url: '/pms/ap-main/ap-detail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得报支记录明细档
export function getApDetail(id) {
  return request({
    url: '/pms/ap-main/ap-detail/get?id=' + id,
    method: 'get'
  })
}

export function getApDetailTotal(parentid) {
  return request({
    url: '/pms/ap-main/ap-detail/total?parentid=' + parentid,
    method: 'get'
  })
}
// ==================== 子表（报支记录凭证档） ====================
// 获得报支记录凭证档分页
export function getApDetail2Page(params) {
  return request({
    url: '/pms/ap-main/ap-detail2/page',
    method: 'get',
    params
  })
}

// 新增报支记录凭证档
export function createApDetail2(data) {
  return request({
    url: '/pms/ap-main/ap-detail2/create',
    method: 'post',
    data
  })
}

// 修改报支记录凭证档
export function updateApDetail2(data) {
  return request({
    url: '/pms/ap-main/ap-detail2/update',
    method: 'post',
    data
  })
}

// 删除报支记录凭证档
export function deleteApDetail2(id) {
  return request({
    url: '/pms/ap-main/ap-detail2/delete?id=' + id,
    method: 'delete'
  })
}

// 获得报支记录凭证档
export function getApDetail2(id) {
  return request({
    url: '/pms/ap-main/ap-detail2/get?id=' + id,
    method: 'get'
  })
}

export function batchOperateApDetail2(data) {
  return request({
    url: '/pms/ap-main/ap-detail2/batchOperate',
    method: 'post',
    data
  })
}
export function batchOperateApDetail1(data) {
  return request({
    url: '/pms/ap-main/ap-detail/batchOperate',
    method: 'post',
    data
  })
}
export function batchOperateApDetail1YF(data) {
  return request({
    url: '/pms/ap-main/ap-detail/batchOperateYF',
    method: 'post',
    data
  })
}

// ==================== 子表（报支记录明细档-付款信息） ====================
// 获得付款信息
export function getApPayInfo(id) {
  return request({
    url: '/pms/ap-main/ap-detail/getPay?id=' + id,
    method: 'get',
  })
}

// 创建付款信息
export function createApPayInfo(data) {
  return request({
    url: '/pms/ap-main/ap-detail/createPay',
    method: 'post',
    data
  })
}

// 创建付款信息
export function updateApPayInfo(data) {
  return request({
    url: '/pms/ap-main/ap-detail/updatePay',
    method: 'post',
    data
  })
}
