<template>
  <div class="app-container">
    <!-- 搜索工作栏 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="集采编号" prop="collectionNo">
        <el-input v-model="queryParams.collectionNo" placeholder="请输入集采编号" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="公司别" prop="companyCode">
        <el-input v-model="queryParams.companyCode" placeholder="请输入公司别" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="生效状态" prop="effectiveStatus">
        <el-select v-model="queryParams.effectiveStatus" placeholder="请选择生效状态" clearable>
          <el-option label="未生效" value="0"/>
          <el-option label="已生效" value="1"/>
          <el-option label="已失效" value="2"/>
        </el-select>
      </el-form-item>
      <el-form-item label="建立日期" prop="createDate">
        <el-date-picker v-model="queryParams.createDate" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                   >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
                   v-hasPermi="['pms:procurement-collection:update']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
                   v-hasPermi="['pms:procurement-collection:delete']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                   v-hasPermi="['pms:procurement-collection:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 主表列表 -->
    <el-table v-loading="loading" :data="procurementCollectionList" @selection-change="handleSelectionChange" @row-click="handleRowClick">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="集采编号" align="center" prop="collectionNo" />
      <el-table-column label="公司别" align="center" prop="companyCode" />
      <el-table-column label="版本号" align="center" prop="versionNo" />
      <el-table-column label="建立人职工编号" align="center" prop="creatorEmpNo" />
      <el-table-column label="建立日期" align="center" prop="createDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="生效状态" align="center" prop="effectiveStatus">
        <template slot-scope="scope">
          <dict-tag :type="DICT_TYPE.PROCUREMENT_EFFECTIVE_STATUS" :value="scope.row.effectiveStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="生效日期" align="center" prop="effectiveDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.effectiveDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['pms:procurement-collection:update']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['pms:procurement-collection:delete']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNo" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 子表区域 -->
    <div v-if="selectedCollection" class="mt20">
      <el-divider content-position="left">
        <span style="font-size: 16px; font-weight: bold;">物料明细 - {{ selectedCollection.collectionNo }}</span>
      </el-divider>

      <!-- 版本选择器 -->
      <div class="mb10">
        <el-select v-model="selectedVersion" placeholder="选择版本" @change="handleVersionChange" style="width: 200px;">
          <el-option label="当前版本" value="current" />
          <el-option v-for="version in versionList" :key="version" :label="'版本 ' + version" :value="version" />
        </el-select>
        <el-button type="primary" size="mini" icon="el-icon-plus" @click="handleAddItem" class="ml10"
                   v-hasPermi="['pms:procurement-collection:create']">新增物料</el-button>
        <el-button type="success" size="mini" icon="el-icon-check" @click="handleBatchSave" class="ml10"
                   v-hasPermi="['pms:procurement-collection:update']">批量保存</el-button>
        <el-button type="info" size="mini" icon="el-icon-time" @click="handleVersionHistory" class="ml10"
                   v-hasPermi="['pms:procurement-collection:query']">版本历史</el-button>
      </div>

      <!-- 子表列表 -->
      <procurement-collection-item-list
        ref="itemList"
        :collection-id="selectedCollection.id"
        :version="selectedVersion"
        @item-change="handleItemChange"
      />
    </div>

    <!-- 添加或修改集采信息主表对话框 -->
    <procurement-collection-form ref="form" @refresh="getList" />

    <!-- 版本历史对话框 -->
    <version-history ref="versionHistory" />
  </div>
</template>

<script>
import { getProcurementCollectionPage, deleteProcurementCollection, exportProcurementCollectionExcel, getVersionsByCollectionId } from "@/api/pms/procurement-collection";
import ProcurementCollectionForm from './ProcurementCollectionForm'
import ProcurementCollectionItemList from './components/ProcurementCollectionItemList'
import VersionHistory from './components/VersionHistory'

export default {
  name: "ProcurementCollection",
  components: {
    ProcurementCollectionForm,
    ProcurementCollectionItemList,
    VersionHistory
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 集采信息主表表格数据
      procurementCollectionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        collectionNo: null,
        companyCode: null,
        effectiveStatus: null,
        createDate: null,
        effectiveDate: null,
        creatorEmpNo: null,
        versionNo: null
      },
      // 表单参数
      form: {},
      // 选中的集采记录
      selectedCollection: null,
      // 版本列表
      versionList: [],
      // 选中的版本
      selectedVersion: 'current',
      // 子表数据变更标识
      itemsChanged: false
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询集采信息主表列表 */
    getList() {
      this.loading = true;
      getProcurementCollectionPage(this.queryParams).then(response => {
        this.procurementCollectionList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        collectionNo: null,
        companyCode: null,
        versionNo: null,
        creatorEmpNo: null,
        createDate: null,
        updaterEmpNo: null,
        updateDate: null,
        closeTime: null,
        effectiveDate: null,
        effectiveStatus: 0,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    // 行点击事件
    handleRowClick(row) {
      this.selectedCollection = row;
      this.selectedVersion = 'current';
      this.loadVersions();
    },
    // 加载版本列表
    loadVersions() {
      if (this.selectedCollection) {
        getVersionsByCollectionId(this.selectedCollection.id).then(response => {
          this.versionList = response.data;
        });
      }
    },
    // 版本切换
    handleVersionChange() {
      if (this.$refs.itemList) {
        this.$refs.itemList.loadItems();
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$refs.form.open();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const id = row.id || this.ids
      this.$refs.form.open(id);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除集采信息主表编号为"' + ids + '"的数据项？').then(function() {
        return deleteProcurementCollection(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('pms/procurement-collection/export-excel', {
        ...this.queryParams
      }, `procurement_collection_${new Date().getTime()}.xls`)
    },
    // 新增物料
    handleAddItem() {
      if (!this.selectedCollection) {
        this.$modal.msgWarning('请先选择集采记录');
        return;
      }
      this.$refs.itemList.handleAdd();
    },
    // 批量保存
    handleBatchSave() {
      if (!this.selectedCollection) {
        this.$modal.msgWarning('请先选择集采记录');
        return;
      }
      this.$refs.itemList.handleBatchSave();
    },
    // 子表数据变更
    handleItemChange() {
      this.itemsChanged = true;
    },
    // 版本历史
    handleVersionHistory() {
      if (!this.selectedCollection) {
        this.$modal.msgWarning('请先选择集采记录');
        return;
      }
      this.$refs.versionHistory.show(this.selectedCollection.id);
    }
  }
};
</script>
