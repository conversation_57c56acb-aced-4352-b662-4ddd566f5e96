import request from '@/utils/request'

// 创建料号索引_技术属性_值
export function createMaterialIndexTechValue(data) {
  return request({
    url: '/basic/material-index-tech-value/create',
    method: 'post',
    data: data
  })
}

// 更新料号索引_技术属性_值
export function updateMaterialIndexTechValue(data) {
  return request({
    url: '/basic/material-index-tech-value/update',
    method: 'put',
    data: data
  })
}

// 删除料号索引_技术属性_值
export function deleteMaterialIndexTechValue(id) {
  return request({
    url: '/basic/material-index-tech-value/delete?id=' + id,
    method: 'delete'
  })
}

// 获得料号索引_技术属性_值
export function getMaterialIndexTechValue(id) {
  return request({
    url: '/basic/material-index-tech-value/get?id=' + id,
    method: 'get'
  })
}

// 获得料号索引_技术属性_值分页
export function getMaterialIndexTechValuePage(params) {
  return request({
    url: '/basic/material-index-tech-value/page',
    method: 'get',
    params
  })
}
// 导出料号索引_技术属性_值 Excel
export function exportMaterialIndexTechValueExcel(params) {
  return request({
    url: '/basic/material-index-tech-value/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function getTechKeyAndValueByMaterialNo(materialNo) {
  return request({
    url: '/basic/material-index-tech-value/getTechKeyAndValueByMaterialNo?materialNo=' + materialNo,
    method: 'get'
  })
}
