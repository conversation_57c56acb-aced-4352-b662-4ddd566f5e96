import request from '@/utils/request'

// 创建进口矿价格
export function createMineralPrices(data) {
  return request({
    url: '/pms/mineral-prices/create',
    method: 'post',
    data: data
  })
}
export function calcDMTU(data) {
  return request({
    url: '/pms/mineral-prices/calc/dmtu',
    method: 'post',
    data: data
  })
}
export function calcAmt(data) {
  return request({
    url: '/pms/mineral-prices/calc/amt',
    method: 'post',
    data: data
  })
}
// 更新进口矿价格
export function updateMineralPrices(data) {
  return request({
    url: '/pms/mineral-prices/update',
    method: 'put',
    data: data
  })
}

// 删除进口矿价格
export function deleteMineralPrices(id) {
  return request({
    url: '/pms/mineral-prices/delete?id=' + id,
    method: 'delete'
  })
}

// 获得进口矿价格
export function getMineralPrices(id) {
  return request({
    url: '/pms/mineral-prices/get?id=' + id,
    method: 'get'
  })
}
export function getFormula(variety) {
  return request({
    url: '/pms/mineral-prices/getFormula?variety=' + variety,
    method: 'get'
  })
}
// 获得进口矿价格分页
export function getMineralPricesPage(params) {
  return request({
    url: '/pms/mineral-prices/page',
    method: 'get',
    params
  })
}
// 导出进口矿价格 Excel
export function exportMineralPricesExcel(params) {
  return request({
    url: '/pms/mineral-prices/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
