import request from '@/utils/request'

// 创建验收批次月累计收发
export function createLocDetailMonCumular(data) {
  return request({
    url: '/pms/loc-detail-mon-cumular/create',
    method: 'post',
    data: data
  })
}

// 更新验收批次月累计收发
export function updateLocDetailMonCumular(data) {
  return request({
    url: '/pms/loc-detail-mon-cumular/update',
    method: 'put',
    data: data
  })
}

// 删除验收批次月累计收发
export function deleteLocDetailMonCumular(id) {
  return request({
    url: '/pms/loc-detail-mon-cumular/delete?id=' + id,
    method: 'delete'
  })
}

// 获得验收批次月累计收发
export function getLocDetailMonCumular(id) {
  return request({
    url: '/pms/loc-detail-mon-cumular/get?id=' + id,
    method: 'get'
  })
}

// 获得验收批次月累计收发分页
export function getLocDetailMonCumularPage(params) {
  return request({
    url: '/pms/loc-detail-mon-cumular/page',
    method: 'get',
    params
  })
}
// 导出验收批次月累计收发 Excel
export function exportLocDetailMonCumularExcel(params) {
  return request({
    url: '/pms/loc-detail-mon-cumular/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
