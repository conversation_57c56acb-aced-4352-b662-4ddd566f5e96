import request from '@/utils/request'

// 创建料号月累计收发
export function createWmsMonCumular(data) {
  return request({
    url: '/pms/wms-mon-cumular/create',
    method: 'post',
    data: data
  })
}

// 更新料号月累计收发
export function updateWmsMonCumular(data) {
  return request({
    url: '/pms/wms-mon-cumular/update',
    method: 'put',
    data: data
  })
}

// 删除料号月累计收发
export function deleteWmsMonCumular(id) {
  return request({
    url: '/pms/wms-mon-cumular/delete?id=' + id,
    method: 'delete'
  })
}

// 获得料号月累计收发
export function getWmsMonCumular(id) {
  return request({
    url: '/pms/wms-mon-cumular/get?id=' + id,
    method: 'get'
  })
}

// 获得料号月累计收发分页
export function getWmsMonCumularPage(params) {
  return request({
    url: '/pms/wms-mon-cumular/page',
    method: 'get',
    params
  })
}
// 导出料号月累计收发 Excel
export function exportWmsMonCumularExcel(params) {
  return request({
    url: '/pms/wms-mon-cumular/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
