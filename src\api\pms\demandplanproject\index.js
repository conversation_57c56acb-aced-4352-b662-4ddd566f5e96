import request from '@/utils/request'

// 创建项目管理
export function createDemandPlanProject(data) {
  return request({
    url: '/pms/demand-plan-project/create',
    method: 'post',
    data: data
  })
}

// 更新项目管理
export function updateDemandPlanProject(data) {
  return request({
    url: '/pms/demand-plan-project/update',
    method: 'put',
    data: data
  })
}

// 删除项目管理
export function deleteDemandPlanProject(id) {
  return request({
    url: '/pms/demand-plan-project/delete?id=' + id,
    method: 'delete'
  })
}

// 获得项目管理
export function getDemandPlanProject(id) {
  return request({
    url: '/pms/demand-plan-project/get?id=' + id,
    method: 'get'
  })
}

// 获得项目管理分页
export function getDemandPlanProjectPage(params) {
  return request({
    url: '/pms/demand-plan-project/page',
    method: 'get',
    params
  })
}

// 导出项目管理 Excel
export function exportDemandPlanProjectExcel(params) {
  return request({
    url: '/pms/demand-plan-project/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
