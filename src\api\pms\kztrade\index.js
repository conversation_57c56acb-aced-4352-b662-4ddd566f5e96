import request from '@/utils/request'

// 创建期初开账
export function createKzTrade(data) {
  return request({
    url: '/pms/kz-trade/create',
    method: 'post',
    data: data
  })
}

// 更新期初开账
export function updateKzTrade(data) {
  return request({
    url: '/pms/kz-trade/update',
    method: 'put',
    data: data
  })
}
// 更新状态
export function updateKzTradeStus(data) {
  return request({
    url: '/pms/kz-trade/updateStus',
    method: 'put',
    data: data
  })
}
// 删除期初开账
export function deleteKzTrade(id) {
  return request({
    url: '/pms/kz-trade/delete?id=' + id,
    method: 'delete'
  })
}

// 获得期初开账
export function getKzTrade(id) {
  return request({
    url: '/pms/kz-trade/get?id=' + id,
    method: 'get'
  })
}

// 获得期初开账分页
export function getKzTradePage(params) {
  return request({
    url: '/pms/kz-trade/page',
    method: 'get',
    params
  })
}
// 导出期初开账 Excel
export function exportKzTradeExcel(params) {
  return request({
    url: '/pms/kz-trade/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（开账管理明细） ====================
    // 获得开账管理明细分页
  export function getKzTradeDetailPage(params) {
    return request({
      url: '/pms/kz-trade/kz-trade-detail/page',
      method: 'get',
      params
    })
  }
        // 新增开账管理明细
  export function createKzTradeDetail(data) {
    return request({
      url: '/pms/kz-trade/kz-trade-detail/create',
      method: 'post',
      data
    })
  }
  // 修改开账管理明细
  export function updateKzTradeDetail(data) {
    return request({
      url: '/pms/kz-trade/kz-trade-detail/update',
      method: 'post',//与controller中的具体方法注释一致postMapping
      data
    })
  }

  // 删除开账管理明细
  export function deleteKzTradeDetail(id) {
    return request({
      url: '/pms/kz-trade/kz-trade-detail/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得开账管理明细
  export function getKzTradeDetail(id) {
    return request({
      url: '/pms/kz-trade/kz-trade-detail/get?id=' + id,
      method: 'get'
    })
  }
// 更新状态
export function updateKzTradeDetailStus(data) {
  return request({
    url: '/pms/kz-trade/kz-trade-detail/updateDetailStus',
    method: 'post',
    data: data
  })
}
//导出模板
export function importTemplate() {
  return request({
    url: '/pms/kz-trade/kz-trade-detail/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}
