import request from '@/utils/request'

// 创建销售订单主档
export function createOrderNo(data) {
  return request({
    url: '/sms/order-no/create',
    method: 'post',
    data: data
  })
}

// 更新销售订单主档
export function updateOrderNo(data) {
  return request({
    url: '/sms/order-no/update',
    method: 'put',
    data: data
  })
}

// 删除销售订单主档
export function deleteOrderNo(id) {
  return request({
    url: '/sms/order-no/delete?id=' + id,
    method: 'delete'
  })
}

// 获得销售订单主档
export function getOrderNo(id) {
  return request({
    url: '/sms/order-no/get?id=' + id,
    method: 'get'
  })
}
export function getByOrderNo(orderNo) {
  return request({
    url: '/sms/order-no/getByOrderNo?orderNo=' + orderNo,
    method: 'get'
  })
}
export function listByProdSpctCodeAndCustNo(prodSpctCode,custNo) {
  return request({
    url: '/sms/order-no/listByProdSpctCodeAndCustNo?prodSpctCode=' + prodSpctCode + '&custNo='+custNo,
    method: 'get'
  })
}
export function cancelConfirmById(id) {
  return request({
    url: '/sms/order-no/cancelConfirmById?id=' + id,
    method: 'delete'
  })
}

// 获得销售订单主档分页
export function getOrderNoPage(params) {
  return request({
    url: '/sms/order-no/page',
    method: 'get',
    params
  })
}
export function getOrderNoVPage(params) {
  return request({
    url: '/sms/order-no/vpage',
    method: 'get',
    params
  })
}
// 导出销售订单主档 Excel
export function exportOrderNoExcel(params) {
  return request({
    url: '/sms/order-no/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 销售订单变更
export function changeOrderErpPoDetail(id) {
  return request({
    url: '/sms/order-no/changeOrderErpPoDetail?id=' + id,
    method: 'get'
  })
}

// ==================== 子表（销售订单项次档） ====================
    // 获得销售订单项次档分页
  export function getOrderItemPage(params) {
    return request({
      url: '/sms/order-no/order-item/page',
      method: 'get',
      params
    })
  }
        // 新增销售订单项次档
  export function createOrderItem(data) {
    return request({
      url: '/sms/order-no/order-item/create',
      method: 'post',
      data
    })
  }
  // 修改销售订单项次档
  export function updateOrderItem(data) {
    return request({
      url: '/sms/order-no/order-item/update',
      method: 'put',
      data
    })
  }
  export function updateOrderItemByV(data) {
    return request({
      url: '/sms/order-no/order-item/updateByV',
      method: 'put',
      data
    })
  }
  // 删除销售订单项次档
  export function deleteOrderItem(id) {
    return request({
      url: '/sms/order-no/order-item/delete?id=' + id,
      method: 'delete'
    })
  }
  export function endcaseOrderItem(id) {
    return request({
      url: '/sms/order-no/order-item/endcase?id=' + id,
      method: 'delete'
    })
  }
  // 获得销售订单项次档
  export function getOrderItem(id) {
    return request({
      url: '/sms/order-no/order-item/get?id=' + id,
      method: 'get'
    })
  }
  // 获得销售订单项次档
  export function listByOrderNoAndProdSpctCode(orderNo,prodSpctCode) {
    return request({
      url: '/sms/order-no/order-item/listByOrderNoAndProdSpctCode?orderNo=' + orderNo + "&prodSpctCode=" + prodSpctCode,
      method: 'get'
    })
  }
  export function getByOrderNOAndOrderItemNO(orderNo, orderItemNo) {
    return request({
      url: '/sms/order-no/order-item/getByOrderNOAndOrderItemNO?orderNo='+orderNo+'&orderItemNo=' + orderItemNo,
      method: 'get'
    })
}
export function pageByParams(data) {
  return request({
    url: '/sms/order-no/order-item/pageByParams',
    method: 'post',
    data
  })
}
export function getOrderQueryPage(data) {
  return request({
    url: '/sms/order-no/orderQueryPage',
    method: 'post',
    data
  })
}
export function orderQueryErpPoList(data) {
  return request({
    url: '/sms/order-no/orderQueryErpPoList',
    method: 'post',
    data
  })
}
export function getOrderErpPoDetail(data) {
  return request({
    url: '/sms/order-no/getOrderErpPoDetail?pono='+data,
    method: 'get',
  })
}

export function getOrderErpSettleDetail(inspNo) {
  return request({
    url: '/sms/order-no/getOrderErpSettleDetail?inspNo='+inspNo,
    method: 'get'
  })
}

export function getPriceByPono(pono) {
  return request({
    url: '/sms/order-no/getPriceByPono?pono='+pono,
    method: 'get'
  })
}
