import request from '@/utils/request'

// -----------------------------------------------组距加价----------------------------------------------
// 创建组距加价
export function createGmRaisePrice(data) {
  return request({
    url: '/sms/gm-raise-price/create',
    method: 'post',
    data: data
  })
}

// 更新组距加价
export function updateGmRaisePrice(data) {
  return request({
    url: '/sms/gm-raise-price/update',
    method: 'put',
    data: data
  })
}

// 删除组距加价
export function deleteGmRaisePrice(id) {
  return request({
    url: '/sms/gm-raise-price/delete?id=' + id,
    method: 'delete'
  })
}

// 获得组距加价
export function getGmRaisePrice(id) {
  return request({
    url: '/sms/gm-raise-price/get?id=' + id,
    method: 'get'
  })
}

// 获得组距加价分页
export function getGmRaisePricePage(params) {
  return request({
    url: '/sms/gm-raise-price/page',
    method: 'get',
    params
  })
}
// 导出组距加价 Excel
export function exportGmRaisePriceExcel(params) {
  return request({
    url: '/sms/gm-raise-price/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// -----------------------------------------------组距加价明细----------------------------------------------
// 创建组距加价明细
export function createGmRaisePriceDetail(data) {
  return request({
    url: '/sms/gm-raise-price/createDetail',
    method: 'post',
    data: data
  })
}

// 更新组距加价明细
export function updateGmRaisePriceDetail(data) {
  return request({
    url: '/sms/gm-raise-price/updateDetail',
    method: 'put',
    data: data
  })
}

// 删除组距加价明细
export function deleteGmRaisePriceDetail(id) {
  return request({
    url: '/sms/gm-raise-price/deleteDetali?id=' + id,
    method: 'delete'
  })
}

// 获得组距加价明细
export function getGmRaisePriceDetail(id) {
  return request({
    url: '/sms/gm-raise-price/getDetali?id=' + id,
    method: 'get'
  })
}

// 获得组距加价分页明细
export function getGmRaisePricePageDetail(params) {
  return request({
    url: '/sms/gm-raise-price/pageDetail',
    method: 'get',
    params
  })
}
// 导出组距加价明细 Excel
export function exportGmRaisePriceExcelDetail(params) {
  return request({
    url: '/sms/gm-raise-price/export-excelDetail',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
