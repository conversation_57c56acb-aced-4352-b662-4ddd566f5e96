import request from '@/utils/request'

// 创建料号索引_技术属性
export function createMaterialIndexTech(data) {
  return request({
    url: '/basic/material-index-tech/create',
    method: 'post',
    data: data
  })
}

// 批量创建/修改
export function batchOperateMaterialIndexTech(data) {
  return request({
    url: '/basic/material-index-tech/batchOperate',
    method: 'post',
    data: data
  })
}

// 更新料号索引_技术属性
export function updateMaterialIndexTech(data) {
  return request({
    url: '/basic/material-index-tech/update',
    method: 'put',
    data: data
  })
}

// 删除料号索引_技术属性
export function deleteMaterialIndexTech(id) {
  return request({
    url: '/basic/material-index-tech/delete?id=' + id,
    method: 'delete'
  })
}

// 获得料号索引_技术属性
export function getMaterialIndexTech(id) {
  return request({
    url: '/basic/material-index-tech/get?id=' + id,
    method: 'get'
  })
}

// 获得料号索引_技术属性分页
export function getMaterialIndexTechPage(params) {
  return request({
    url: '/basic/material-index-tech/page',
    method: 'get',
    params
  })
}

// 获得料号索引_技术属性分页 全部数据
export function getMaterialIndexTechPageAll(params) {
  return request({
    url: '/basic/material-index-tech/pageAll',
    method: 'get',
    params
  })
}
// 导出料号索引_技术属性 Excel
export function exportMaterialIndexTechExcel(params) {
  return request({
    url: '/basic/material-index-tech/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
