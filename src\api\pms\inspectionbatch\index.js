import request from '@/utils/request'

// 创建验收批次档
export function createInspectionBatch(data) {
  return request({
    url: '/pms/inspection-batch/create',
    method: 'post',
    data: data
  })
}

// 更新验收批次档
export function updateInspectionBatch(data) {
  return request({
    url: '/pms/inspection-batch/update',
    method: 'put',
    data: data
  })
}

// 删除验收批次档
export function deleteInspectionBatch(id) {
  return request({
    url: '/pms/inspection-batch/delete?id=' + id,
    method: 'delete'
  })
}

// 获得验收批次档
export function getInspectionBatch(id) {
  return request({
    url: '/pms/inspection-batch/get?id=' + id,
    method: 'get'
  })
}

// 获得验收批次档分页
export function getInspectionBatchPage(params) {
  return request({
    url: '/pms/inspection-batch/page',
    method: 'get',
    params
  })
}
// 导出验收批次档 Excel
export function exportInspectionBatchExcel(params) {
  return request({
    url: '/pms/inspection-batch/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（验收批次化物性档） ====================
    // 获得验收批次化物性档分页
  export function getInspectionBatchPhysicalPage(params) {
    return request({
      url: '/pms/inspection-batch/inspection-batch-physical/page',
      method: 'get',
      params
    })
  }
        // 新增验收批次化物性档
  export function createInspectionBatchPhysical(data) {
    return request({
      url: '/pms/inspection-batch/inspection-batch-physical/create',
      method: 'post',
      data
    })
  }
  // 修改验收批次化物性档
  export function updateInspectionBatchPhysical(data) {
    return request({
      url: '/pms/inspection-batch/inspection-batch-physical/update',
      method: 'put',
      data
    })
  }
  export function loadAllChkitem(id) {
    return request({
      url: '/pms/inspection-batch/inspection-batch-physical/loadAllChkitem?id='+id,
      method: 'put'
    })
  }
  // 删除验收批次化物性档
  export function deleteInspectionBatchPhysical(id) {
    return request({
      url: '/pms/inspection-batch/inspection-batch-physical/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得验收批次化物性档
  export function getInspectionBatchPhysical(id) {
    return request({
      url: '/pms/inspection-batch/inspection-batch-physical/get?id=' + id,
      method: 'get'
    })
  }
