import request from '@/utils/request'

// 创建采购案主档管理
export function createInq(data) {
  return request({
    url: '/pms/inq/create',
    method: 'post',
    data: data
  })
}

// 更新采购案主档管理
export function updateInq(data) {
  return request({
    url: '/pms/inq/update',
    method: 'put',
    data: data
  })
}

// 删除采购案主档管理
export function deleteInq(id) {
  return request({
    url: '/pms/inq/delete?id=' + id,
    method: 'delete'
  })
}

// 获得采购案主档管理
export function getInq(id) {
  return request({
    url: '/pms/inq/get?id=' + id,
    method: 'get'
  })
}

// 获得采购案主档管理分页
export function getInqPage(params) {
  return request({
    url: '/pms/inq/page',
    method: 'get',
    params
  })
}

// 导出采购案主档管理 Excel
export function exportInqExcel(params) {
  return request({
    url: '/pms/inq/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 提交采购案主档管理
export function submitInq(id) {
  return request({
    url: '/pms/inq/submit?id=' + id,
    method: 'get'
  })
}
// 取消提交采购案主档管理
export function cancelSubmitInq(id) {
  return request({
    url: '/pms/inq/cancelSubmit?id=' + id,
    method: 'get'
  })
}
// 提交采购案主档管理至已签合同
export function submitInqPono(id) {
  return request({
    url: '/pms/inq/submitPono?id=' + id,
    method: 'get'
  })
}
// 作废采购案主档管理
export function invalidateInq(id) {
  return request({
    url: '/pms/inq/invalidate?id=' + id,
    method: 'get'
  })
}

// ==================== 子表（采购案项次档管理） ====================
// 获得采购案项次档管理分页
export function getInqDetailPage(params) {
  return request({
    url: '/pms/inq/inq-detail/page',
    method: 'get',
    params
  })
}

// ==================== 子表（采购案项次档管理） ====================
// 获得采购案项次档管理分页
export function getInqDetailPage2(params) {
  return request({
    url: '/pms/inq/inq-detail/page2',
    method: 'get',
    params
  })
}

// 新增采购案项次档管理
export function createInqDetail(data) {
  return request({
    url: '/pms/inq/inq-detail/create',
    method: 'post',
    data
  })
}

// 修改采购案项次档管理
export function updateInqDetail(data) {
  return request({
    url: '/pms/inq/inq-detail/update',
    method: 'post',
    data
  })
}

// 删除采购案项次档管理
export function deleteInqDetail(id) {
  return request({
    url: '/pms/inq/inq-detail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得采购案项次档管理
export function getInqDetail(id) {
  return request({
    url: '/pms/inq/inq-detail/get?id=' + id,
    method: 'get'
  })
}

// 获得采购案项次档管理
export function batchOperate(data) {
  return request({
    url: '/pms/inq/inq-detail/batchOperate',
    method: 'post',
    data
  })
}
