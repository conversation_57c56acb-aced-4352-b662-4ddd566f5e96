import request from '@/utils/request'

// 创建销售配货装车主档
export function createLoadWgt(data) {
  return request({
    url: '/sms/load-wgt/create',
    method: 'post',
    data: data
  })
}

// 更新销售配货装车主档
export function updateLoadWgt(data) {
  return request({
    url: '/sms/load-wgt/update',
    method: 'put',
    data: data
  })
}

// 删除销售配货装车主档
export function deleteLoadWgt(id) {
  return request({
    url: '/sms/load-wgt/delete?id=' + id,
    method: 'delete'
  })
}

// 获得销售配货装车主档
export function getLoadWgt(id) {
  return request({
    url: '/sms/load-wgt/get?id=' + id,
    method: 'get'
  })
}

export function writeoffLoadWgt(id) {
  return request({
    url: '/sms/load-wgt/writeoff?id=' + id,
    method: 'get'
  })
}

export function antiWriteoffLoadWgt(id) {
  return request({
    url: '/sms/load-wgt/antiWriteoff?id=' + id,
    method: 'get'
  })
}

// 获得销售配货装车主档分页
export function getLoadWgtPage(params) {
  return request({
    url: '/sms/load-wgt/page',
    method: 'get',
    params
  })
}

// 导出销售配货装车主档 Excel
export function exportLoadWgtExcel(params) {
  return request({
    url: '/sms/load-wgt/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（销售配货装车项次档） ====================
// 导出销售配货装车主档 Excel
export function getLoadItemWgtExcelData(params) {
  return request({
    url: '/sms/load-wgt/load-item-wgt/getLoadItemWgtExcelData',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获得销售配货装车项次档分页
export function getLoadItemWgtPage(params) {
  return request({
    url: '/sms/load-wgt/load-item-wgt/page',
    method: 'get',
    params
  })
}

// 新增销售配货装车项次档
export function createLoadItemWgt(data) {
  return request({
    url: '/sms/load-wgt/load-item-wgt/create',
    method: 'post',
    data
  })
}

// 修改销售配货装车项次档
export function updateLoadItemWgt(data) {
  return request({
    url: '/sms/load-wgt/load-item-wgt/update',
    method: 'post',
    data
  })
}

// 删除销售配货装车项次档
export function deleteLoadItemWgt(id) {
  return request({
    url: '/sms/load-wgt/load-item-wgt/delete?id=' + id,
    method: 'delete'
  })
}

// 获得销售配货装车项次档
export function getLoadItemWgt(id) {
  return request({
    url: '/sms/load-wgt/load-item-wgt/get?id=' + id,
    method: 'get'
  })
}

export function getItemPageByParams(params) {
  return request({
    url: '/sms/load-wgt/load-item-wgt/itemPageByParams',
    method: 'get',
    params
  })
}

export function ifSameDetail(data) {
  return request({
    url: '/sms/load-wgt/load-item-wgt/ifSameDetail',
    method: 'post',
    data
  })
}

export function importTemplateSaleTypeC(saleType) {
  return request({
    url: '/sms/load-wgt/load-item-wgt/get-import-template?saleType=' + saleType,
    method: 'get',
    responseType: 'blob'
  })
}

export function importTemplateSaleTypeA() {
  return request({
    url: '/sms/load-wgt/load-item-wgt/get-import-template-saleTypeA',
    method: 'get',
    responseType: 'blob'
  })
}

export function orderQueryErpSettleList(data) {
  return request({
    url: '/sms/load-wgt/orderQueryErpSettleList',
    method: 'post',
    data
  })
}

export function batchCreateByErp(data) {
  return request({
    url: '/sms/load-wgt/batchCreateByErp',
    method: 'post',
    data
  })
}

export function createLoadItemByErp(data) {
  return request({
    url: '/sms/load-wgt/createLoadItemByErp',
    method: 'post',
    data
  })
}

// ==================== 子表（出库单关联合同项次档） ====================
// 获得出库单关联合同项次档分页
export function getLoadItemLinkPonoPage(params) {
  return request({
    url: '/sms/load-wgt/load-item-link-pono/page',
    method: 'get',
    params
  })
}
export function getLoadItemLinkPonoPage2(params) {
  return request({
    url: '/sms/load-wgt/load-item-link-pono/page2',
    method: 'get',
    params
  })
}

export function getLoadItemLinkPonoPagegroupByList(params) {
  return request({
    url: '/sms/load-wgt/load-item-link-pono/groupByList',
    method: 'get',
    params
  })
}

// 新增出库单关联合同项次档
export function createLoadItemLinkPono(data) {
  return request({
    url: '/sms/load-wgt/load-item-link-pono/create',
    method: 'post',
    data
  })
}

// 修改出库单关联合同项次档
export function updateLoadItemLinkPono(data) {
  return request({
    url: '/sms/load-wgt/load-item-link-pono/update',
    method: 'post',
    data
  })
}

// 删除出库单关联合同项次档
export function deleteLoadItemLinkPono(id) {
  return request({
    url: '/sms/load-wgt/load-item-link-pono/delete?id=' + id,
    method: 'delete'
  })
}

// 获得出库单关联合同项次档
export function getLoadItemLinkPono(id) {
  return request({
    url: '/sms/load-wgt/load-item-link-pono/get?id=' + id,
    method: 'get'
  })
}

// 批量操作 出库单关联项次表
export function batchOperateLoadItemLinkPono(data) {
  return request({
    url: '/sms/load-wgt/load-item-link-pono/batchOperateLoadItemLinkPono',
    method: 'post',
    data
  })
}

// 获得没有国贸合同号的出库单项次档
export function getLoadItemNoChooseList(parentid) {
  return request({
    url: '/sms/load-wgt/load-item-wgt/getLoadItemNoChooseList?parentid=' + parentid,
    method: 'get'
  })
}

// ==================== 子表（费用明细） ====================
// 获得费用明细分页
export function getLoadExpenseDetailPage(params) {
  return request({
    url: '/sms/load-wgt/load-expense-detail/page',
    method: 'get',
    params
  })
}
// 获得费用明细分页
export function getLoadExpenseDetailPage2(params) {
  return request({
    url: '/sms/load-wgt/load-expense-detail/page2',
    method: 'get',
    params
  })
}

// 新增费用明细
export function createLoadExpenseDetail(data) {
  return request({
    url: '/sms/load-wgt/load-expense-detail/create',
    method: 'post',
    data
  })
}
// 修改费用明细
export function updateLoadExpenseDetail(data) {
  return request({
    url: '/sms/load-wgt/load-expense-detail/update',
    method: 'post',
    data
  })
}
// 删除费用明细
export function deleteLoadExpenseDetail(id) {
  return request({
    url: '/sms/load-wgt/load-expense-detail/delete?id=' + id,
    method: 'delete'
  })
}
// 获得费用明细
export function getLoadExpenseDetail(id) {
  return request({
    url: '/sms/load-wgt/load-expense-detail/get?id=' + id,
    method: 'get'
  })
}
// 获得出库项次档汇总的销售订单号
export function getGroupByOrderNo(parentid) {
  return request({
    url: '/sms/load-wgt/load-item-wgt/getGroupByOrderNo?parentid=' + parentid,
    method: 'get'
  })
}
// 获得出库项次档汇总的销售订单号
export function batchOperateLoadExpenseDetail(data) {
  return request({
    url: '/sms/load-wgt/load-expense-detail/batchOperateLoadExpenseDetail',
    method: 'post',
    data
  })
}
