import request from '@/utils/request'

// 创建汇率
export function createExchangeRate(data) {
  return request({
    url: '/system/exchange-rate/create',
    method: 'post',
    data: data
  })
}

// 更新汇率
export function updateExchangeRate(data) {
  return request({
    url: '/system/exchange-rate/update',
    method: 'put',
    data: data
  })
}

// 删除汇率
export function deleteExchangeRate(id) {
  return request({
    url: '/system/exchange-rate/delete?id=' + id,
    method: 'delete'
  })
}

// 获得汇率
export function getExchangeRate(id) {
  return request({
    url: '/system/exchange-rate/get?id=' + id,
    method: 'get'
  })
}

// 获得汇率分页
export function getExchangeRatePage(params) {
  return request({
    url: '/system/exchange-rate/page',
    method: 'get',
    params
  })
}

// 导出汇率 Excel
export function exportExchangeRateExcel(params) {
  return request({
    url: '/system/exchange-rate/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获得汇率分页
export function getExchangeRateDetails(params) {
  return request({
    url: '/system/exchange-rate/pageDetails',
    method: 'get',
    params
  })
}

// 获得汇率分页
export function getExchangeRatesByMonth(params) {
  return request({
    url: '/system/exchange-rate/monthly',
    method: 'get',
    params
  })
}

// 获得汇率分页
export function getRateList(params) {
  return request({
    url: '/system/exchange-rate/getRateList',
    method: 'get',
    params
  })
}

