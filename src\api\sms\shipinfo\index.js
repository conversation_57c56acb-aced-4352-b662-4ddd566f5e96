import request from '@/utils/request'

// 创建船信息
export function createShipInfo(data) {
  return request({
    url: '/sms/ship-info/create',
    method: 'post',
    data: data
  })
}

// 更新船信息
export function updateShipInfo(data) {
  return request({
    url: '/sms/ship-info/update',
    method: 'put',
    data: data
  })
}

// 删除船信息
export function deleteShipInfo(id) {
  return request({
    url: '/sms/ship-info/delete?id=' + id,
    method: 'delete'
  })
}

// 获得船信息
export function getShipInfo(id) {
  return request({
    url: '/sms/ship-info/get?id=' + id,
    method: 'get'
  })
}

// 获得船信息分页
export function getShipInfoPage(params) {
  return request({
    url: '/sms/ship-info/page',
    method: 'get',
    params
  })
}
// 导出船信息 Excel
export function exportShipInfoExcel(params) {
  return request({
    url: '/sms/ship-info/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
