import request from '@/utils/request'

// 创建TT尾款发票
export function createTtFinalInvoice(data) {
  return request({
    url: '/sms/tt-final-invoice/create',
    method: 'post',
    data: data
  })
}

// 更新TT尾款发票
export function updateTtFinalInvoice(data) {
  return request({
    url: '/sms/tt-final-invoice/update',
    method: 'put',
    data: data
  })
}

// 删除TT尾款发票
export function deleteTtFinalInvoice(id) {
  return request({
    url: '/sms/tt-final-invoice/delete?id=' + id,
    method: 'delete'
  })
}

// 获得TT尾款发票
export function getTtFinalInvoice(id) {
  return request({
    url: '/sms/tt-final-invoice/get?id=' + id,
    method: 'get'
  })
}

// 获得TT尾款发票分页
export function getTtFinalInvoicePage(params) {
  return request({
    url: '/sms/tt-final-invoice/page',
    method: 'get',
    params
  })
}
// 导出TT尾款发票 Excel
export function exportTtFinalInvoiceExcel(params) {
  return request({
    url: '/sms/tt-final-invoice/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（TT尾款发票明细） ====================
      // 获得TT尾款发票明细列表
    export function getTtFinalInvoiceDetailListByParentid(parentid) {
      return request({
        url: '/sms/tt-final-invoice/tt-final-invoice-detail/list-by-parentid?parentid=' + parentid,
        method: 'get'
      })
    }
