import request from '@/utils/request'

// 创建船运验收入储主档
export function createInspShipMain(data) {
  return request({
    url: '/pms/insp-ship-main/create',
    method: 'post',
    data: data
  })
}

// 更新船运验收入储主档
export function updateInspShipMain(data) {
  return request({
    url: '/pms/insp-ship-main/update',
    method: 'post',
    data: data
  })
}

// 删除船运验收入储主档
export function deleteInspShipMain(id) {
  return request({
    url: '/pms/insp-ship-main/delete?id=' + id,
    method: 'delete'
  })
}

// 获得船运验收入储主档
export function getInspShipMain(id) {
  return request({
    url: '/pms/insp-ship-main/get?id=' + id,
    method: 'get'
  })
}

// 获得船运验收入储主档分页
export function getInspShipMainPage(params) {
  return request({
    url: '/pms/insp-ship-main/page',
    method: 'get',
    params
  })
}

// 导出船运验收入储主档 Excel
export function exportInspShipMainExcel(params) {
  return request({
    url: '/pms/insp-ship-main/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（验收批次化物性档） ====================
// 获得验收批次化物性档分页
export function getInspectionBatchPhysicalPage(params) {
  return request({
    url: '/pms/insp-ship-main/inspection-batch-physical/page',
    method: 'get',
    params
  })
}

// 新增验收批次化物性档
export function createInspectionBatchPhysical(data) {
  return request({
    url: '/pms/insp-ship-main/inspection-batch-physical/create',
    method: 'post',
    data
  })
}

// 修改验收批次化物性档
export function updateInspectionBatchPhysical(data) {
  return request({
    url: '/pms/insp-ship-main/inspection-batch-physical/update',
    method: 'post',
    data
  })
}

// 删除验收批次化物性档
export function deleteInspectionBatchPhysical(id) {
  return request({
    url: '/pms/insp-ship-main/inspection-batch-physical/delete?id=' + id,
    method: 'delete'
  })
}

// 获得验收批次化物性档
export function getInspectionBatchPhysical(id) {
  return request({
    url: '/pms/insp-ship-main/inspection-batch-physical/get?id=' + id,
    method: 'get'
  })
}

// 获得验收批次化物性档
export function getCOAH2OByPono(pono) {
  return request({
    url: '/pms/insp-ship-main/inspection-batch-physical/getCOAH2OByPono?pono=' + pono,
    method: 'get'
  })
}

// ==================== 子表（CIQ指标） ====================
// 获得CIQ指标分页
export function getInspectionBatchPhysiCal2Page(params) {
  return request({
    url: '/pms/insp-ship-main/inspection-batch-physi-cal2/page',
    method: 'get',
    params
  })
}

// 新增CIQ指标
export function createInspectionBatchPhysiCal2(data) {
  return request({
    url: '/pms/insp-ship-main/inspection-batch-physi-cal2/create',
    method: 'post',
    data
  })
}

// 修改CIQ指标
export function updateInspectionBatchPhysiCal2(data) {
  return request({
    url: '/pms/insp-ship-main/inspection-batch-physi-cal2/update',
    method: 'post',
    data
  })
}

// 删除CIQ指标
export function deleteInspectionBatchPhysiCal2(id) {
  return request({
    url: '/pms/insp-ship-main/inspection-batch-physi-cal2/delete?id=' + id,
    method: 'delete'
  })
}

// 获得CIQ指标
export function getInspectionBatchPhysiCal2(id) {
  return request({
    url: '/pms/insp-ship-main/inspection-batch-physi-cal2/get?id=' + id,
    method: 'get'
  })
}

// 确认合同管理主表信息
export function confirmInspShipMain(id, flag) {
  return request({
    url: '/pms/insp-ship-main/confirm?id=' + id + '&flag=' + flag,
    method: 'get'
  })
}

// 确认合同管理主表信息
export function cancelConfirmInspShipMain(id, flag) {
  return request({
    url: '/pms/insp-ship-main/cancelConfirm?id=' + id + '&flag=' + flag,
    method: 'get'
  })
}

// 获得船运验收入储主档分页
export function getStoreMonCalPage(params) {
  return request({
    url: '/pms/insp-ship-main/monCalPage',
    method: 'get',
    params
  })
}

// 获得船运验收入储主档分页
export function getDictColumn(params) {
  return request({
    url: '/pms/insp-ship-main/dictColumn',
    method: 'get',
    params
  })
}

// 导出日月指数 Excel
export function exportStoreMonCalIndexExcel(params) {
  return request({
    url: '/pms/insp-ship-main/export-store-mon-cal-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 确认合同管理主表信息
export function cleanUpInspShipMain(id) {
  return request({
    url: '/pms/insp-ship-main/cleanUp?id=' + id ,
    method: 'get'
  })
}

// 取消确认合同管理主表信息
export function cancelCleanUpInspShipMain(id) {
  return request({
    url: '/pms/insp-ship-main/cancelCleanUp?id=' + id,
    method: 'get'
  })
}

// 获得船运验收入储主档
export function getInspShipMainByPo(pono) {
  return request({
    url: '/pms/insp-ship-main/getByPo?pono=' + pono,
    method: 'get'
  })
}

