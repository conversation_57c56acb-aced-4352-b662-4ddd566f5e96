import request from '@/utils/request'

// 创建外贸合同交接
export function createGmContractTransfer(data) {
  return request({
    url: '/sms/gm-contract-transfer/create',
    method: 'post',
    data: data
  })
}

// 更新外贸合同交接
export function updateGmContractTransfer(data) {
  return request({
    url: '/sms/gm-contract-transfer/update',
    method: 'put',
    data: data
  })
}

// 删除外贸合同交接
export function deleteGmContractTransfer(id) {
  return request({
    url: '/sms/gm-contract-transfer/delete?id=' + id,
    method: 'delete'
  })
}

// 获得外贸合同交接
export function getGmContractTransfer(id) {
  return request({
    url: '/sms/gm-contract-transfer/get?id=' + id,
    method: 'get'
  })
}

// 获得外贸合同交接分页
export function getGmContractTransferPage(params) {
  return request({
    url: '/sms/gm-contract-transfer/page',
    method: 'get',
    params
  })
}
// 导出外贸合同交接 Excel
export function exportGmContractTransferExcel(params) {
  return request({
    url: '/sms/gm-contract-transfer/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 生效流程
export function effectiveContractTransfer(id) {
  return request({
    url: '/sms/gm-contract-transfer/effect?id=' + id,
    method: 'get'
  })
}
