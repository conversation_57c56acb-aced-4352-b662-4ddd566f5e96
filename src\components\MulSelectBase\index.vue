<template>
  <div class="mul-select-base" :class="{disabled}">
    <div class="mul-select-base__list">
      <span v-if="!disabled&&selectList.length<=0" class="placeholder">{{ placeholder }}</span>
      <template v-else>
        <el-tag class="mul-select-base-item" v-for="item in selectList" :key="item.id" :closable="!disabled"
                @close="deleteItem(item)">
          <component :is="isLink?'el-link':'span'" class="mul-select-base-item__label text-overflow-ellipsis"
                     :href="item.label" target="_blank">
            {{ isLink ? getFileName(item.label) : item.label }}
          </component>
        </el-tag>
      </template>
    </div>
    <el-button class="control-operation" v-if="!disabled"  type="primary" @click="$emit('select')">
      选择
    </el-button>
  </div>
</template>

<script>
import {getFileName} from "@/utils/common";

export default {
  name: "MulSelectBase",
  props: {
    ids: {
      type: String|Number,
      default: undefined
    },
    labels: {
      type: String,
      default: undefined
    },
    //是否显示选择按钮
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    //label是否是可以点击的url
    isLink: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    selectList() {
      if (!this.ids || !this.labels) return []
      const idsArr = (''+this.ids).split(',')
      const labelsArr = this.labels.split(',')
      return idsArr.map((item, index) => ({
        id: item,
        label: labelsArr[index]
      }))
    },
  },
  methods: {
    getFileName,
    //删除某一项
    deleteItem(item) {
      const filterArr = this.selectList.filter(el => el.id !== item.id)
      const ids = filterArr.map(item => item.id).join(',')
      const labels = filterArr.map(item => item.label).join(',')
      this.$emit('update:ids', ids)
      this.$emit('update:labels', labels)
    }
  },
};
</script>

<style lang="scss" scoped>
.mul-select-base {
  width: 100%;
  display: flex;
  align-items: center;

  &.disabled {
    .mul-select-base__list {
      border: none;
      padding: 0;
      background: initial;
    }
  }


  .mul-select-base__list {
    flex: 1;
    overflow: hidden;
    padding: 2px 10px;
    border: 1px solid #dadce2;
    border-radius: 4px;
    background: #fff;

    .placeholder {
      color: #c6c9d1;
    }


    .mul-select-base-item {
      display: inline-flex;
      overflow: hidden;
      align-items: center;
      max-width: 100%;
      margin: 4px;

      .mul-select-base-item__label {
        flex: 1;
        display: block;
      }
    }
  }

  .control-operation {
    margin-right: 10px;
  }
}
</style>
