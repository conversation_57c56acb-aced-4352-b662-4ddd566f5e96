import request from '@/utils/request'

// 创建关账日期
export function createAccountClose(data) {
  return request({
    url: '/pms/account-close/create',
    method: 'post',
    data: data
  })
}

// 更新关账日期
export function updateAccountClose(data) {
  return request({
    url: '/pms/account-close/update',
    method: 'put',
    data: data
  })
}

// 删除关账日期
export function deleteAccountClose(id) {
  return request({
    url: '/pms/account-close/delete?id=' + id,
    method: 'delete'
  })
}

// 获得关账日期
export function getAccountClose(id) {
  return request({
    url: '/pms/account-close/get?id=' + id,
    method: 'get'
  })
}

// 获得关账日期分页
export function getAccountClosePage(params) {
  return request({
    url: '/pms/account-close/page',
    method: 'get',
    params
  })
}
// 导出关账日期 Excel
export function exportAccountCloseExcel(params) {
  return request({
    url: '/pms/account-close/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 确认关账
export function confirmAccountClose(id) {
  return request({
    url: '/pms/account-close/confirm?id=' + id,
    method: 'get',
  })
}
