import request from '@/utils/request'

// 创建目的/装船国
export function createItForeignCountry(data) {
  return request({
    url: '/sms/basicinfo/it-foreign-country/create',
    method: 'post',
    data: data
  })
}

// 更新目的/装船国
export function updateItForeignCountry(data) {
  return request({
    url: '/sms/basicinfo/it-foreign-country/update',
    method: 'put',
    data: data
  })
}

// 删除目的/装船国
export function deleteItForeignCountry(id) {
  return request({
    url: '/sms/basicinfo/it-foreign-country/delete?id=' + id,
    method: 'delete'
  })
}

// 获得目的/装船国
export function getItForeignCountry(id) {
  return request({
    url: '/sms/basicinfo/it-foreign-country/get?id=' + id,
    method: 'get'
  })
}

// 获得目的/装船国分页
export function getItForeignCountryPage(params) {
  return request({
    url: '/sms/basicinfo/it-foreign-country/page',
    method: 'get',
    params
  })
}
// 获得目的/装船国分页
export function getAllItForeignCountryPage(params) {
  return request({
    url: '/sms/basicinfo/it-foreign-country/allpage',
    method: 'get',
    params
  })
}
// 导出目的/装船国 Excel
export function exportItForeignCountryExcel(params) {
  return request({
    url: '/sms/basicinfo/it-foreign-country/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

//=============================================客户基本资料===================================================

// 创建客户基本资料
export function createItForeignCustInfo(data) {
  return request({
    url: '/sms/basicinfo/it-foreign-cust-info/create',
    method: 'post',
    data: data
  })
}

// 更新客户基本资料
export function updateItForeignCustInfo(data) {
  return request({
    url: '/sms/basicinfo/it-foreign-cust-info/update',
    method: 'put',
    data: data
  })
}

// 删除客户基本资料
export function deleteItForeignCustInfo(id) {
  return request({
    url: '/sms/basicinfo/it-foreign-cust-info/delete?id=' + id,
    method: 'delete'
  })
}

// 获得客户基本资料
export function getItForeignCustInfo(id) {
  return request({
    url: '/sms/basicinfo/it-foreign-cust-info/get?id=' + id,
    method: 'get'
  })
}

// 获得客户基本资料分页
export function getItForeignCustInfoPage(params) {
  return request({
    url: '/sms/basicinfo/it-foreign-cust-info/page',
    method: 'get',
    params
  })
}
// 导出客户基本资料 Excel
export function exportItForeignCustInfoExcel(params) {
  return request({
    url: '/sms/basicinfo/it-foreign-cust-info/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

//=============================================目的/装船港===================================================

// 创建目的/装船港
export function createItForeignPort(data) {
  return request({
    url: '/sms/basicinfo/it-foreign-port/create',
    method: 'post',
    data: data
  })
}

// 更新目的/装船港
export function updateItForeignPort(data) {
  return request({
    url: '/sms/basicinfo/it-foreign-port/update',
    method: 'put',
    data: data
  })
}

// 删除目的/装船港
export function deleteItForeignPort(id) {
  return request({
    url: '/sms/basicinfo/it-foreign-port/delete?id=' + id,
    method: 'delete'
  })
}

// 获得目的/装船港
export function getItForeignPort(id) {
  return request({
    url: '/sms/basicinfo/it-foreign-port/get?id=' + id,
    method: 'get'
  })
}

// 获得目的/装船港分页
export function getItForeignPortPage(params) {
  return request({
    url: '/sms/basicinfo/it-foreign-port/page',
    method: 'get',
    params
  })
}
// 导出目的/装船港 Excel
export function exportItForeignPortExcel(params) {
  return request({
    url: '/sms/basicinfo/it-foreign-port/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

//=============================================产品定价===================================================

// 创建产品定价
export function createItProdPrice(data) {
  return request({
    url: '/sms/basicinfo/it-prod-price/create',
    method: 'post',
    data: data
  })
}

// 更新产品定价
export function updateItProdPrice(data) {
  return request({
    url: '/sms/basicinfo/it-prod-price/update',
    method: 'put',
    data: data
  })
}

// 删除产品定价
export function deleteItProdPrice(id) {
  return request({
    url: '/sms/basicinfo/it-prod-price/delete?id=' + id,
    method: 'delete'
  })
}

// 获得产品定价
export function getItProdPrice(id) {
  return request({
    url: '/sms/basicinfo/it-prod-price/get?id=' + id,
    method: 'get'
  })
}

// 获得产品定价分页
export function getItProdPricePage(params) {
  return request({
    url: '/sms/basicinfo/it-prod-price/page',
    method: 'get',
    params
  })
}
// 导出产品定价 Excel
export function exportItProdPriceExcel(params) {
  return request({
    url: '/sms/basicinfo/it-prod-price/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
//生效流程
export function effectiveProdPrice(id) {
  return request({
    url: '/sms/basicinfo/it-prod-price/effect?id=' + id,
    method: 'get'
  })
}

export function invalidProdPrice(id) {
  return request({
    url: '/sms/basicinfo/it-prod-price/invalid?id=' + id,
    method: 'get'
  })
}

//=============================================产品定价明细===================================================

// 创建产品定价明细
export function createProdPriceDetail(data) {
  return request({
    url: '/sms/prod-price-detail/create',
    method: 'post',
    data: data
  })
}

// 更新产品定价明细
export function updateProdPriceDetail(data) {
  return request({
    url: '/sms/prod-price-detail/update',
    method: 'put',
    data: data
  })
}

// 删除产品定价明细
export function deleteProdPriceDetail(id) {
  return request({
    url: '/sms/prod-price-detail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得产品定价明细
export function getProdPriceDetail(id) {
  return request({
    url: '/sms/prod-price-detail/get?id=' + id,
    method: 'get'
  })
}

// 获得产品定价明细分页
export function getProdPriceDetailPage(params) {
  return request({
    url: '/sms/prod-price-detail/page',
    method: 'get',
    params
  })
}

// 获得产品定价明细分页
export function getAllProdPriceDetailPage(params) {
  return request({
    url: '/sms/prod-price-detail/allpage',
    method: 'get',
    params
  })
}

//=============================================产品资源设定===================================================

// 创建产品资源设定
export function createItProdResourceAssign(data) {
  return request({
    url: '/sms/basicinfo/it-prod-resource-assign/create',
    method: 'post',
    data: data
  })
}

// 更新产品资源设定
export function updateItProdResourceAssign(data) {
  return request({
    url: '/sms/basicinfo/it-prod-resource-assign/update',
    method: 'put',
    data: data
  })
}

// 删除产品资源设定
export function deleteItProdResourceAssign(id) {
  return request({
    url: '/sms/basicinfo/it-prod-resource-assign/delete?id=' + id,
    method: 'delete'
  })
}

// 获得产品资源设定
export function getItProdResourceAssign(id) {
  return request({
    url: '/sms/basicinfo/it-prod-resource-assign/get?id=' + id,
    method: 'get'
  })
}

// 获得产品资源设定分页
export function getItProdResourceAssignPage(params) {
  return request({
    url: '/sms/basicinfo/it-prod-resource-assign/page',
    method: 'get',
    params
  })
}
// 导出产品资源设定 Excel
export function exportItProdResourceAssignExcel(params) {
  return request({
    url: '/sms/basicinfo/it-prod-resource-assign/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

