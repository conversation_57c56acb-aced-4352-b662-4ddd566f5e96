import request from '@/utils/request'

// 创建应付帐款暂估
export function createEpDetail(data) {
  return request({
    url: '/pms/ep-detail/create',
    method: 'post',
    data: data
  })
}

// 更新应付帐款暂估
export function updateEpDetail(data) {
  return request({
    url: '/pms/ep-detail/update',
    method: 'put',
    data: data
  })
}

// 删除应付帐款暂估
export function deleteEpDetail(id) {
  return request({
    url: '/pms/ep-detail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得应付帐款暂估
export function getEpDetail(id) {
  return request({
    url: '/pms/ep-detail/get?id=' + id,
    method: 'get'
  })
}

// 获得应付帐款暂估分页
export function getEpDetailPage(params) {
  return request({
    url: '/pms/ep-detail/page',
    method: 'get',
    params
  })
}
// 导出应付帐款暂估 Excel
export function exportEpDetailExcel(params) {
  return request({
    url: '/pms/ep-detail/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function getEpDetailPageByApDetail1(params) {
  return request({
    url: '/pms/ep-detail/pageByApDetail1',
    method: 'get',
    params
  })
}
