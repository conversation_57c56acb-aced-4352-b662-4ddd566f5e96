import request from '@/utils/request'

// 创建库存配置管理
export function createStockConfig(data) {
  return request({
    url: '/pms/stock-config/create',
    method: 'post',
    data: data
  })
}

// 更新库存配置管理
export function updateStockConfig(data) {
  return request({
    url: '/pms/stock-config/update',
    method: 'put',
    data: data
  })
}

// 删除库存配置管理
export function deleteStockConfig(id) {
  return request({
    url: '/pms/stock-config/delete?id=' + id,
    method: 'delete'
  })
}

// 获得库存配置管理
export function getStockConfig(id) {
  return request({
    url: '/pms/stock-config/get?id=' + id,
    method: 'get'
  })
}

// 获得库存配置管理分页
export function getStockConfigPage(params) {
  return request({
    url: '/pms/stock-config/page',
    method: 'get',
    params
  })
}
// 导出库存配置管理 Excel
export function exportStockConfigExcel(params) {
  return request({
    url: '/pms/stock-config/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
