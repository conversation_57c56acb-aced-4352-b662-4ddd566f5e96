import request from '@/utils/request'

// 创建交易过程记录
export function createTradeAll(data) {
  return request({
    url: '/pms/trade-all/create',
    method: 'post',
    data: data
  })
}

// 更新交易过程记录
export function updateTradeAll(data) {
  return request({
    url: '/pms/trade-all/update',
    method: 'put',
    data: data
  })
}

// 删除交易过程记录
export function deleteTradeAll(id) {
  return request({
    url: '/pms/trade-all/delete?id=' + id,
    method: 'delete'
  })
}

// 获得交易过程记录
export function getTradeAll(id) {
  return request({
    url: '/pms/trade-all/get?id=' + id,
    method: 'get'
  })
}

// 获得交易过程记录分页
export function getTradeAllPage(params) {
  return request({
    url: '/pms/trade-all/page',
    method: 'get',
    params
  })
}
// 导出交易过程记录 Excel
export function exportTradeAllExcel(params) {
  return request({
    url: '/pms/trade-all/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
