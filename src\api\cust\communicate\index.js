import request from '@/utils/request'

// 创建沟通记录
export function createCommunicate(data) {
  return request({
    url: '/cust/communicate/create',
    method: 'post',
    data: data
  })
}

// 更新沟通记录
export function updateCommunicate(data) {
  return request({
    url: '/cust/communicate/update',
    method: 'put',
    data: data
  })
}

// 删除沟通记录
export function deleteCommunicate(id) {
  return request({
    url: '/cust/communicate/delete?id=' + id,
    method: 'delete'
  })
}

// 获得沟通记录
export function getCommunicate(id) {
  return request({
    url: '/cust/communicate/get?id=' + id,
    method: 'get'
  })
}

// 获得沟通记录分页
export function getCommunicatePage(params) {
  return request({
    url: '/cust/communicate/page',
    method: 'get',
    params
  })
}
// 导出沟通记录 Excel
export function exportCommunicateExcel(params) {
  return request({
    url: '/cust/communicate/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
