import request from '@/utils/request'

// 创建发票信息
export function createInvoiceInfo(data) {
  return request({
    url: '/pms/invoice-info/create',
    method: 'post',
    data: data
  })
}

// 更新发票信息
export function updateInvoiceInfo(data) {
  return request({
    url: '/pms/invoice-info/update',
    method: 'put',
    data: data
  })
}

// 删除发票信息
export function deleteInvoiceInfo(id) {
  return request({
    url: '/pms/invoice-info/delete?id=' + id,
    method: 'delete'
  })
}

// 获得发票信息
export function getInvoiceInfo(id) {
  return request({
    url: '/pms/invoice-info/get?id=' + id,
    method: 'get'
  })
}

// 获得发票信息分页
export function getInvoiceInfoPage(params) {
  return request({
    url: '/pms/invoice-info/page',
    method: 'get',
    params
  })
}

// 导出发票信息 Excel
export function exportInvoiceInfoExcel(params) {
  return request({
    url: '/pms/invoice-info/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获得发票信息
export function confirmInvoiceInfo(id, flag) {
  return request({
    url: '/pms/invoice-info/confirm?id=' + id + '&flag=' + flag,
    method: 'get'
  })
}

// 创建合同审批
export function createInvoiceAudit(data) {
  return request({
    url: '/pms/invoice-info/audit/create',
    method: 'post',
    data: data
  })
}
