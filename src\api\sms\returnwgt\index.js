import request from '@/utils/request'

// 创建退货单主档
export function createReturnWgt(data) {
  return request({
    url: '/sms/return-wgt/create',
    method: 'post',
    data: data
  })
}

// 更新退货单主档
export function updateReturnWgt(data) {
  return request({
    url: '/sms/return-wgt/update',
    method: 'put',
    data: data
  })
}

// 删除退货单主档
export function deleteReturnWgt(id) {
  return request({
    url: '/sms/return-wgt/delete?id=' + id,
    method: 'delete'
  })
}

// 获得退货单主档
export function getReturnWgt(id) {
  return request({
    url: '/sms/return-wgt/get?id=' + id,
    method: 'get'
  })
}

// 获得退货单主档分页
export function getReturnWgtPage(params) {
  return request({
    url: '/sms/return-wgt/page',
    method: 'get',
    params
  })
}

// 导出退货单主档 Excel
export function exportReturnWgtExcel(params) {
  return request({
    url: '/sms/return-wgt/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（退货单明细档） ====================
// 获得退货单明细档分页
export function getReturnWgtDetailPage(params) {
  return request({
    url: '/sms/return-wgt/return-wgt-detail/page',
    method: 'get',
    params
  })
}

// 新增退货单明细档
export function createReturnWgtDetail(data) {
  return request({
    url: '/sms/return-wgt/return-wgt-detail/create',
    method: 'post',
    data
  })
}

// 修改退货单明细档
export function updateReturnWgtDetail(data) {
  return request({
    url: '/sms/return-wgt/return-wgt-detail/update',
    method: 'post',
    data
  })
}

// 删除退货单明细档
export function deleteReturnWgtDetail(id) {
  return request({
    url: '/sms/return-wgt/return-wgt-detail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得退货单明细档
export function getReturnWgtDetail(id) {
  return request({
    url: '/sms/return-wgt/return-wgt-detail/get?id=' + id,
    method: 'get'
  })
}

export function ifSameDetail(data) {
  return request({
    url: '/sms/return-wgt/return-wgt-detail/ifSameDetail',
    method: 'post',
    data
  })
}

export function returnWriteoff(id) {
  return request({
    url: '/sms/return-wgt/returnWriteoff?id=' + id,
    method: 'get'
  })
}
export function cancelReturnWriteoff(id) {
  return request({
    url: '/sms/return-wgt/cancelReturnWriteoff?id=' + id,
    method: 'get'
  })
}

