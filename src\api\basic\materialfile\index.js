import request from '@/utils/request'

// 创建料号附件
export function createMaterialFile(data) {
  return request({
    url: '/basic/material-file/create',
    method: 'post',
    data: data
  })
}

// 更新料号附件
export function updateMaterialFile(data) {
  return request({
    url: '/basic/material-file/update',
    method: 'put',
    data: data
  })
}

// 删除料号附件
export function deleteMaterialFile(id) {
  return request({
    url: '/basic/material-file/delete?id=' + id,
    method: 'delete'
  })
}

// 获得料号附件
export function getMaterialFile(id) {
  return request({
    url: '/basic/material-file/get?id=' + id,
    method: 'get'
  })
}

// 获得料号附件分页
export function getMaterialFilePage(params) {
  return request({
    url: '/basic/material-file/page',
    method: 'get',
    params
  })
}
// 导出料号附件 Excel
export function exportMaterialFileExcel(params) {
  return request({
    url: '/basic/material-file/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获得料号附件
export function getMaterialFileByParentid(parentid) {
  return request({
    url: '/basic/material-file/getByParentid?parentid=' + parentid,
    method: 'get'
  })
}
