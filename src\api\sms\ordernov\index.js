import request from '@/utils/request'

// 创建销售订单主档历史版本
export function createOrderNoV(data) {
  return request({
    url: '/sms/order-no-v/create',
    method: 'post',
    data: data
  })
}

// 更新销售订单主档历史版本
export function updateOrderNoV(data) {
  return request({
    url: '/sms/order-no-v/update',
    method: 'put',
    data: data
  })
}

// 删除销售订单主档历史版本
export function deleteOrderNoV(id) {
  return request({
    url: '/sms/order-no-v/delete?id=' + id,
    method: 'delete'
  })
}

// 获得销售订单主档历史版本
export function getOrderNoV(id) {
  return request({
    url: '/sms/order-no-v/get?id=' + id,
    method: 'get'
  })
}
export function getAllVerByMainId(mainid) {
  return request({
    url: '/sms/order-no-v/getAllVerByMainId?mainid=' + mainid,
    method: 'get'
  })
}

// 获得销售订单主档历史版本分页
export function getOrderNoVPage(params) {
  return request({
    url: '/sms/order-no-v/page',
    method: 'get',
    params
  })
}
// 导出销售订单主档历史版本 Excel
export function exportOrderNoVExcel(params) {
  return request({
    url: '/sms/order-no-v/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（销售订单项次档历史版本） ====================
    // 获得销售订单项次档历史版本分页
  export function getOrderItemVPage(params) {
    return request({
      url: '/sms/order-no-v/order-item-v/page',
      method: 'get',
      params
    })
  }
  export function getOrderItemVPageByMainid(params) {
    return request({
      url: '/sms/order-no-v/order-item-v/pagebymainid',
      method: 'get',
      params
    })
  }
        // 新增销售订单项次档历史版本
  export function createOrderItemV(data) {
    return request({
      url: '/sms/order-no-v/order-item-v/create',
      method: 'post',
      data
    })
  }
  // 修改销售订单项次档历史版本
  export function updateOrderItemV(data) {
    return request({
      url: '/sms/order-no-v/order-item-v/update',
      method: 'post',
      data
    })
  }
  // 删除销售订单项次档历史版本
  export function deleteOrderItemV(id) {
    return request({
      url: '/sms/order-no-v/order-item-v/delete?id=' + id,
      method: 'delete'
    })
  }
  // 获得销售订单项次档历史版本
  export function getOrderItemV(id) {
    return request({
      url: '/sms/order-no-v/order-item-v/get?id=' + id,
      method: 'get'
    })
  }
