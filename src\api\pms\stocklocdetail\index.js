import request from '@/utils/request'

// 创建实时库存明细
export function createWmsStockLocDetail(data) {
  return request({
    url: '/pms/wms-stock-loc-detail/create',
    method: 'post',
    data: data
  })
}

// 更新实时库存明细
export function updateWmsStockLocDetail(data) {
  return request({
    url: '/pms/wms-stock-loc-detail/update',
    method: 'put',
    data: data
  })
}

// 删除实时库存明细
export function deleteWmsStockLocDetail(id) {
  return request({
    url: '/pms/wms-stock-loc-detail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得实时库存明细
export function getWmsStockLocDetail(id) {
  return request({
    url: '/pms/wms-stock-loc-detail/get?id=' + id,
    method: 'get'
  })
}

// 获得实时库存明细分页
export function getWmsStockLocDetailPage(params) {
  return request({
    url: '/pms/wms-stock-loc-detail/page',
    method: 'get',
    params
  })
}
// 导出实时库存明细 Excel
export function exportWmsStockLocDetailExcel(params) {
  return request({
    url: '/pms/wms-stock-loc-detail/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
