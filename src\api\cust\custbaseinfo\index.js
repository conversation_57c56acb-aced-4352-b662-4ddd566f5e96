import request from '@/utils/request'

// 创建客戶基本信息
export function createCustBaseInfo(data) {
  return request({
    url: '/cust/custbaseinfo/create',
    method: 'post',
    data: dataCustBaseInfo
  })
}

// 更新客戶基本信息
export function updateCustBaseInfo(data) {
  return request({
    url: '/cust/custbaseinfo/update',
    method: 'put',
    data: data
  })
}

// 获得客戶基本信息
export function getCustBaseInfo(custNo) {
  return request({
    url: '/cust/custbaseinfo/get?custNo=' + custNo,
    method: 'get'
  })
}

// 获得客戶基本信息分页
export function getCustBaseInfoPage(params) {
  return request({
    url: '/cust/custbaseinfo/page',
    method: 'get',
    params
  })
}
// 导出客戶基本信息 Excel
export function exportCustBaseInfoExcel(params) {
  return request({
    url: '/cust/custbaseinfo/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
