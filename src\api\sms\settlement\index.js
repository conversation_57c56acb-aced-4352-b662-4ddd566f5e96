import request from '@/utils/request'

// 创建结算数据资料档
export function createSettlement(data) {
  return request({
    url: '/sms/settlement/create',
    method: 'post',
    data: data
  })
}

// 更新结算数据资料档
export function updateSettlement(data) {
  return request({
    url: '/sms/settlement/update',
    method: 'put',
    data: data
  })
}

// 删除结算数据资料档
export function deleteSettlement(id) {
  return request({
    url: '/sms/settlement/delete?id=' + id,
    method: 'delete'
  })
}

// 获得结算数据资料档
export function getSettlement(id) {
  return request({
    url: '/sms/settlement/get?id=' + id,
    method: 'get'
  })
}

// 获得结算数据资料档分页
export function getSettlementPage(params) {
  return request({
    url: '/sms/settlement/page',
    method: 'get',
    params
  })
}
// 导出结算数据资料档 Excel
export function exportSettlementExcel(params) {
  return request({
    url: '/sms/settlement/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
export function toCancelSettlement(data) {
  return request({
    url: '/sms/settlement/toCancelSettlement',
    method: 'post',
    data
  })
}
export function endCaseQueryPage(data) {
  return request({
    url: '/sms/settlement/endCaseQueryPage',
    method: 'post',
    data
  })
}
