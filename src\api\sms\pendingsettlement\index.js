import request from '@/utils/request'

// 创建待结算资料档
export function createPendingSettlement(data) {
  return request({
    url: '/sms/pending-settlement/create',
    method: 'post',
    data: data
  })
}

// 更新待结算资料档
export function updatePendingSettlement(data) {
  return request({
    url: '/sms/pending-settlement/update',
    method: 'put',
    data: data
  })
}

// 删除待结算资料档
export function deletePendingSettlement(id) {
  return request({
    url: '/sms/pending-settlement/delete?id=' + id,
    method: 'delete'
  })
}

// 获得待结算资料档
export function getPendingSettlement(id) {
  return request({
    url: '/sms/pending-settlement/get?id=' + id,
    method: 'get'
  })
}

// 获得待结算资料档分页
export function getPendingSettlementPage(params) {
  return request({
    url: '/sms/pending-settlement/page',
    method: 'get',
    params
  })
}
// 导出待结算资料档 Excel
export function exportPendingSettlementExcel(params) {
  return request({
    url: '/sms/pending-settlement/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
export function toSettlement(data) {
  return request({
    url: '/sms/pending-settlement/toSettlement',
    method: 'post',
    data
  })
}
