import request from '@/utils/request'

// 创建合同条款设定
export function createContractualTerms(data) {
  return request({
    url: '/pms/contractual-terms/create',
    method: 'post',
    data: data
  })
}

// 更新合同条款设定
export function updateContractualTerms(data) {
  return request({
    url: '/pms/contractual-terms/update',
    method: 'put',
    data: data
  })
}

// 删除合同条款设定
export function deleteContractualTerms(id) {
  return request({
    url: '/pms/contractual-terms/delete?id=' + id,
    method: 'delete'
  })
}

// 获得合同条款设定
export function getContractualTerms(id) {
  return request({
    url: '/pms/contractual-terms/get?id=' + id,
    method: 'get'
  })
}

// 获得合同条款设定分页
export function getContractualTermsPage(params) {
  return request({
    url: '/pms/contractual-terms/page',
    method: 'get',
    params
  })
}

// 导出合同条款设定 Excel
export function exportContractualTermsExcel(params) {
  return request({
    url: '/pms/contractual-terms/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function confirmContractualTerms(id) {
  return request({
    url: '/pms/contractual-terms/confirm?id=' + id,
    method: 'get'
  })
}
export function cancelConfirmContractualTerms(id) {
  return request({
    url: '/pms/contractual-terms/cancelConfirm?id=' + id,
    method: 'get'
  })
}
export function getContractualTermsAll() {
  return request({
    url: '/pms/contractual-terms/getAllList',
    method: 'get'
  })
}
