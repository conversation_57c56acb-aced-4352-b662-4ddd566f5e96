import request from '@/utils/request'

// 创建储位料号月累计收发
export function createWmsLocMonCumular(data) {
  return request({
    url: '/pms/wms-loc-mon-cumular/create',
    method: 'post',
    data: data
  })
}

// 更新储位料号月累计收发
export function updateWmsLocMonCumular(data) {
  return request({
    url: '/pms/wms-loc-mon-cumular/update',
    method: 'put',
    data: data
  })
}

// 删除储位料号月累计收发
export function deleteWmsLocMonCumular(id) {
  return request({
    url: '/pms/wms-loc-mon-cumular/delete?id=' + id,
    method: 'delete'
  })
}

// 获得储位料号月累计收发
export function getWmsLocMonCumular(id) {
  return request({
    url: '/pms/wms-loc-mon-cumular/get?id=' + id,
    method: 'get'
  })
}

// 获得储位料号月累计收发分页
export function getWmsLocMonCumularPage(params) {
  return request({
    url: '/pms/wms-loc-mon-cumular/page',
    method: 'get',
    params
  })
}
// 导出储位料号月累计收发 Excel
export function exportWmsLocMonCumularExcel(params) {
  return request({
    url: '/pms/wms-loc-mon-cumular/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获得储位料号月累计收发分页
export function carryOverWmsLocMonCumular(params) {
  return request({
    url: '/pms/wms-loc-mon-cumular/carryOver',
    method: 'get',
    params
  })
}
