import request from '@/utils/request'

// 创建物料验收
export function createGrMain(data) {
  return request({
    url: '/pms/gr-main/create',
    method: 'post',
    data: data
  })
}

// 更新物料验收
export function updateGrMain(data) {
  return request({
    url: '/pms/gr-main/update',
    method: 'put',
    data: data
  })
}

// 删除物料验收
export function deleteGrMain(id) {
  return request({
    url: '/pms/gr-main/delete?id=' + id,
    method: 'delete'
  })
}

// 获得物料验收
export function getGrMain(id) {
  return request({
    url: '/pms/gr-main/get?id=' + id,
    method: 'get'
  })
}

// 获得物料验收分页
export function getGrMainPage(params) {
  return request({
    url: '/pms/gr-main/page',
    method: 'get',
    params
  })
}
// 获得物料验收分页
export function getGrMainMPage(params) {
  return request({
    url: '/pms/gr-main/mpage',
    method: 'get',
    params
  })
}

// 获得确认验收
export function confirmGrMain(id) {
  return request({
    url: '/pms/gr-main/confirm?id=' + id,
    method: 'get'
  })
}

// 获得确认验收
export function confirmCheckGrMain(id) {
  return request({
    url: '/pms/gr-main/confirmCheck?id=' + id,
    method: 'post'
  })
}

// 获得确认验收
export function continueGrMain(id, stus) {
  return request({
    url: '/pms/gr-main/continue?id=' + id + '&stus=' + stus,
    method: 'get'
  })
}

// 取消确认验收
export function cancelConfirmGrMain(id) {
  return request({
    url: '/pms/gr-main/cancelConfirm?id=' + id,
    method: 'get'
  })
}

// 导出物料验收 Excel
export function exportGrMainExcel(params) {
  return request({
    url: '/pms/gr-main/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function getGrMainPageQuery(params) {
  return request({
    url: '/pms/gr-main/pageQuery',
    method: 'get',
    params
  })
}
// ==================== 子表（物料验收明细） ====================
// 获得物料验收明细分页
export function getGrDetailPage(params) {
  return request({
    url: '/pms/gr-main/gr-detail/page',
    method: 'get',
    params
  })
}

// 新增物料验收明细
export function createGrDetail(data) {
  return request({
    url: '/pms/gr-main/gr-detail/create',
    method: 'post',
    data
  })
}

// 修改物料验收明细
export function updateGrDetail(data) {
  return request({
    url: '/pms/gr-main/gr-detail/update',
    method: 'post',
    data
  })
}

// 删除物料验收明细
export function deleteGrDetail(id) {
  return request({
    url: '/pms/gr-main/gr-detail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得物料验收明细
export function getGrDetail(id) {
  return request({
    url: '/pms/gr-main/gr-detail/get?id=' + id,
    method: 'get'
  })
}


// ==================== 子表（结算明细） ====================
// 获得结算明细分页
export function getPpDetail1Page(params) {
  return request({
    url: '/pms/gr-main/pp-detail1/page',
    method: 'get',
    params
  })
}

// 新增结算明细
export function createPpDetail1(data) {
  return request({
    url: '/pms/gr-main/pp-detail1/create',
    method: 'post',
    data
  })
}

// 修改结算明细
export function updatePpDetail1(data) {
  return request({
    url: '/pms/gr-main/pp-detail1/update',
    method: 'post',
    data
  })
}

// 删除结算明细
export function deletePpDetail1(id) {
  return request({
    url: '/pms/gr-main/pp-detail1/delete?id=' + id,
    method: 'delete'
  })
}

// 获得结算明细
export function getPpDetail1(id) {
  return request({
    url: '/pms/gr-main/pp-detail1/get?id=' + id,
    method: 'get'
  })
}

// ==================== 子表（结算金额调整） ====================
// 获得结算金额调整分页
export function getPpDetail2Page(params) {
  return request({
    url: '/pms/gr-main/pp-detail2/page',
    method: 'get',
    params
  })
}

// 新增结算金额调整
export function createPpDetail2(data) {
  return request({
    url: '/pms/gr-main/pp-detail2/create',
    method: 'post',
    data
  })
}

// 修改结算金额调整
export function updatePpDetail2(data) {
  return request({
    url: '/pms/gr-main/pp-detail2/update',
    method: 'post',
    data
  })
}

// 删除结算金额调整
export function deletePpDetail2(id) {
  return request({
    url: '/pms/gr-main/pp-detail2/delete?id=' + id,
    method: 'delete'
  })
}

// 获得结算金额调整
export function getPpDetail2(id) {
  return request({
    url: '/pms/gr-main/pp-detail2/get?id=' + id,
    method: 'get'
  })
}

// ==================== 子表（总结算） ====================
// 获得总结算分页
export function getPpDetail3Page(params) {
  return request({
    url: '/pms/gr-main/pp-detail3/page',
    method: 'get',
    params
  })
}

// 新增总结算
export function createPpDetail3(data) {
  return request({
    url: '/pms/gr-main/pp-detail3/create',
    method: 'post',
    data
  })
}

// 修改总结算
export function updatePpDetail3(data) {
  return request({
    url: '/pms/gr-main/pp-detail3/update',
    method: 'post',
    data
  })
}

// 删除总结算
export function deletePpDetail3(id) {
  return request({
    url: '/pms/gr-main/pp-detail3/delete?id=' + id,
    method: 'delete'
  })
}

// 获得总结算
export function getPpDetail3(id) {
  return request({
    url: '/pms/gr-main/pp-detail3/get?id=' + id,
    method: 'get'
  })
}

//批量插入结算明细
export function batchInsertPpDetail1(reqList) {
  return request({
    url: '/pms/gr-main/pp-detail1/batch',
    method: 'post',
    data: reqList
  })
}

//批量插入结算明细
export function calSettle(id) {
  return request({
    url: '/pms/gr-main/pp-detail3/calSettle?id=' + id,
    method: 'get'
  })
}

//获得检查相关的上传文件
export function getCheckGrDetailRelations(params) {
	return request({
		url: '/pms/gr-main/gr-detail/getCheckGrDetailRelations',
		method: 'GET',
		params: params
	})
}

// 获得物料交易中间分页
export function batchSplitCaseGrMain(data) {
  return request({
    url: '/pms/gr-main/gr-detail/batchSplit',
    method: 'post',
    data: data
  })
}

// 批量新增
export function batchInsertGrDetails(data) {
  return request({
    url: '/pms/gr-main/gr-detail/batch',
    method: 'post',
    data: data
  })
}

export function batchSettleInsert(data) {
  return request({
    url: '/pms/gr-main/gr-detail/batchSettle',
    method: 'post',
    data: data
  })
}

// 创建结算暂估明细
export function createSettlementDetail(data) {
  return request({
    url: '/pms/gr-main/settlement-detail/create',
    method: 'post',
    data: data
  })
}

// 更新结算暂估明细
export function updateSettlementDetail(data) {
  return request({
    url: '/pms/gr-main/settlement-detail/update',
    method: 'put',
    data: data
  })
}

// 删除结算暂估明细
export function deleteSettlementDetail(id) {
  return request({
    url: '/pms/gr-main/settlement-detail/delete?id=' + id,
    method: 'delete'
  })
}

// 获得结算暂估明细
export function getSettlementDetail(id) {
  return request({
    url: '/pms/gr-main/settlement-detail/get?id=' + id,
    method: 'get'
  })
}

// 获得结算暂估明细分页
export function getSettlementDetailPage(params) {
  return request({
    url: '/pms/gr-main/settlement-detail/page',
    method: 'get',
    params
  })
}

// 下载结算暂估明细
export function downloadList(parentId) {
  return request({
    url: '/pms/gr-main/settlement-detail/download?parentId=' + parentId,
    method: 'get'
  })
}

// 删除结算暂估明细
export function getTotalList(parentId) {
  return request({
    url: '/pms/gr-main/settlement-detail/total?parentId=' + parentId,
    method: 'get'
  })
}


